# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/service_haven_customer_verification

# Experian/CrossCore API Configuration
EXPERIAN_API_URL=https://api.experian.com
EXPERIAN_API_KEY=your_experian_api_key_here
EXPERIAN_CLIENT_ID=your_experian_client_id_here
EXPERIAN_CLIENT_SECRET=your_experian_client_secret_here

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_here
WEBHOOK_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
JWT_SECRET=your_jwt_secret_here
API_KEY=your_api_key_here

# Rate Limiting Configuration
RATE_LIMIT_WINDOW=3600
RATE_LIMIT_MAX_REQUESTS=1000

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Email Configuration (for notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
FROM_EMAIL=<EMAIL>

# Monitoring Configuration
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_new_relic_key_here
