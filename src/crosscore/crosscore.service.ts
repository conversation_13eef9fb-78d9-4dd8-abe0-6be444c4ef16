import { CrosscoreRepository } from './crosscore.repository';
import { CrosscoreMapper } from './crosscore.mapper';
import { getErrorMessage } from '../common/utils';
import {
  CreateCrosscoreRequest,
  UpdateCrosscoreRequest,
  GetCrosscoreQuery,
  CrosscoreResponse,
  PaginatedCrosscoreResponse,
  CrosscoreWebhookPayload,
} from './crosscore.dto';

export class CrosscoreService {
  private repository: CrosscoreRepository;

  constructor() {
    this.repository = new CrosscoreRepository();
  }

  /**
   * Creates a new crosscore verification request
   */
  async createRequest(
    request: CreateCrosscoreRequest
  ): Promise<CrosscoreResponse> {
    try {
      const entityData = CrosscoreMapper.toEntity(request);
      const crosscoreRequest = await this.repository.create(entityData);

      // Here you would typically call the Experian API
      // For now, we'll just update the status to in_progress
      await this.repository.updateById(crosscoreRequest.id, {
        status: 'in_progress',
      });

      const updatedRequest = await this.repository.findById(
        crosscoreRequest.id
      );
      return CrosscoreMapper.toResponse(updatedRequest!);
    } catch (error) {
      throw new Error(
        `Failed to create crosscore request: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Gets a crosscore request by ID
   */
  async getRequestById(id: string): Promise<CrosscoreResponse | null> {
    try {
      const request = await this.repository.findById(id);
      if (!request) {
        return null;
      }
      return CrosscoreMapper.toResponse(request);
    } catch (error) {
      throw new Error(
        `Failed to get crosscore request: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Gets a crosscore request by request ID
   */
  async getRequestByRequestId(
    requestId: string
  ): Promise<CrosscoreResponse | null> {
    try {
      const request = await this.repository.findByRequestId(requestId);
      if (!request) {
        return null;
      }
      return CrosscoreMapper.toResponse(request);
    } catch (error) {
      throw new Error(
        `Failed to get crosscore request: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Gets crosscore requests with filtering and pagination
   */
  async getRequests(
    query: GetCrosscoreQuery
  ): Promise<PaginatedCrosscoreResponse> {
    try {
      const { requests, total } = await this.repository.findMany(query);
      const page = query.page || 1;
      const limit = query.limit || 10;

      return CrosscoreMapper.toPaginatedResponse(requests, page, limit, total);
    } catch (error) {
      throw new Error(
        `Failed to get crosscore requests: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Gets all requests for a specific user
   */
  async getRequestsByUserId(userId: string): Promise<CrosscoreResponse[]> {
    try {
      const requests = await this.repository.findByUserId(userId);
      return CrosscoreMapper.toResponseArray(requests);
    } catch (error) {
      throw new Error(`Failed to get user requests: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Updates a crosscore request
   */
  async updateRequest(
    id: string,
    request: UpdateCrosscoreRequest
  ): Promise<CrosscoreResponse | null> {
    try {
      const updateData = CrosscoreMapper.toUpdateEntity(request);
      const updatedRequest = await this.repository.updateById(id, updateData);
      if (!updatedRequest) {
        return null;
      }
      return CrosscoreMapper.toResponse(updatedRequest);
    } catch (error) {
      throw new Error(
        `Failed to update crosscore request: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Cancels a crosscore request
   */
  async cancelRequest(id: string): Promise<CrosscoreResponse | null> {
    try {
      const updateData = {
        status: 'cancelled' as const,
        completedAt: new Date(),
      };
      const request = await this.repository.updateById(id, updateData);
      if (!request) {
        return null;
      }
      return CrosscoreMapper.toResponse(request);
    } catch (error) {
      throw new Error(
        `Failed to cancel crosscore request: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Processes a webhook from Experian/CrossCore
   */
  async processWebhook(
    webhookPayload: CrosscoreWebhookPayload
  ): Promise<CrosscoreResponse | null> {
    try {
      // Find the request by requestId or experianRequestId
      let request = await this.repository.findByRequestId(
        webhookPayload.requestId
      );

      if (!request && webhookPayload.experianRequestId) {
        request = await this.repository.findByExperianRequestId(
          webhookPayload.experianRequestId
        );
      }

      if (!request) {
        throw new Error(
          `Request not found for webhook: ${webhookPayload.requestId}`
        );
      }

      // Map webhook data to update entity
      const updateData = CrosscoreMapper.webhookToUpdateEntity({
        requestId: webhookPayload.requestId,
        experianRequestId: webhookPayload.experianRequestId,
        status: webhookPayload.status,
        data: webhookPayload.data,
      });

      const updatedRequest = await this.repository.updateById(
        request.id,
        updateData
      );

      if (!updatedRequest) {
        throw new Error('Failed to update request with webhook data');
      }

      return CrosscoreMapper.toResponse(updatedRequest);
    } catch (error) {
      throw new Error(`Failed to process webhook: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Gets requests that are pending webhook responses
   */
  async getPendingWebhooks(): Promise<CrosscoreResponse[]> {
    try {
      const requests = await this.repository.findPendingWebhooks();
      return CrosscoreMapper.toResponseArray(requests);
    } catch (error) {
      throw new Error(
        `Failed to get pending webhooks: ${getErrorMessage(error)}`
      );
    }
  }

  /**
   * Gets crosscore request statistics
   */
  async getStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byVerificationType: Record<string, number>;
    webhookStats: {
      received: number;
      pending: number;
    };
  }> {
    try {
      return await this.repository.getStatistics();
    } catch (error) {
      throw new Error(`Failed to get statistics: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Simulates calling Experian API (placeholder for actual implementation)
   */
  private async callExperianAPI(
    verificationType: string,
    requestData: Record<string, any>
  ): Promise<{ experianRequestId: string; status: string }> {
    // This is a placeholder - in real implementation, you would call Experian's API
    // and return the actual response
    return {
      experianRequestId: `EXP_${Date.now()}`,
      status: 'submitted',
    };
  }
}
