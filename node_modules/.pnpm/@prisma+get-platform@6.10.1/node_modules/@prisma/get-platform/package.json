{"name": "@prisma/get-platform", "version": "6.10.1", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/get-platform"}, "bugs": "https://github.com/prisma/prisma/issues", "devDependencies": {"@codspeed/benchmark.js-plugin": "4.0.0", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/jest": "29.5.14", "@types/node": "18.19.76", "benchmark": "2.1.4", "jest": "29.7.0", "jest-junit": "16.0.0", "typescript": "5.4.5", "escape-string-regexp": "5.0.0", "execa": "5.1.1", "fs-jetpack": "5.1.0", "kleur": "4.1.5", "strip-ansi": "6.0.1", "tempy": "1.0.1", "terminal-link": "4.0.0", "ts-pattern": "5.6.2"}, "dependencies": {"@prisma/debug": "6.10.1"}, "files": ["README.md", "dist"], "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "jest"}}