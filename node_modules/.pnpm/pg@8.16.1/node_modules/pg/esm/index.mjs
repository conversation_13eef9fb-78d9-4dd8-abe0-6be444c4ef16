// ESM wrapper for pg
import pg from '../lib/index.js'

// Re-export all the properties
export const Client = pg.Client
export const Pool = pg.Pool
export const Connection = pg.Connection
export const types = pg.types
export const Query = pg.Query
export const DatabaseError = pg.DatabaseError
export const escapeIdentifier = pg.escapeIdentifier
export const escapeLiteral = pg.escapeLiteral
export const Result = pg.Result
export const TypeOverrides = pg.TypeOverrides

// Also export the defaults
export const defaults = pg.defaults

// Re-export the default
export default pg
