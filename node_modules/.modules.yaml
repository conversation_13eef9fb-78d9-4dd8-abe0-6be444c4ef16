hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@hapi/accept@6.0.3':
    '@hapi/accept': private
  '@hapi/address@4.1.0':
    '@hapi/address': private
  '@hapi/ammo@6.0.1':
    '@hapi/ammo': private
  '@hapi/b64@6.0.1':
    '@hapi/b64': private
  '@hapi/boom@10.0.1':
    '@hapi/boom': private
  '@hapi/bounce@3.0.2':
    '@hapi/bounce': private
  '@hapi/bourne@3.0.0':
    '@hapi/bourne': private
  '@hapi/call@9.0.1':
    '@hapi/call': private
  '@hapi/catbox-memory@6.0.2':
    '@hapi/catbox-memory': private
  '@hapi/catbox@12.1.1':
    '@hapi/catbox': private
  '@hapi/content@6.0.0':
    '@hapi/content': private
  '@hapi/cryptiles@6.0.1':
    '@hapi/cryptiles': private
  '@hapi/file@3.0.0':
    '@hapi/file': private
  '@hapi/formula@2.0.0':
    '@hapi/formula': private
  '@hapi/heavy@8.0.1':
    '@hapi/heavy': private
  '@hapi/hoek@11.0.7':
    '@hapi/hoek': private
  '@hapi/iron@7.0.1':
    '@hapi/iron': private
  '@hapi/mimos@7.0.1':
    '@hapi/mimos': private
  '@hapi/nigel@5.0.1':
    '@hapi/nigel': private
  '@hapi/pez@6.1.0':
    '@hapi/pez': private
  '@hapi/pinpoint@2.0.1':
    '@hapi/pinpoint': private
  '@hapi/podium@5.0.2':
    '@hapi/podium': private
  '@hapi/shot@6.0.1':
    '@hapi/shot': private
  '@hapi/somever@4.1.1':
    '@hapi/somever': private
  '@hapi/statehood@8.2.0':
    '@hapi/statehood': private
  '@hapi/subtext@8.1.0':
    '@hapi/subtext': private
  '@hapi/teamwork@6.0.0':
    '@hapi/teamwork': private
  '@hapi/topo@6.0.2':
    '@hapi/topo': private
  '@hapi/validate@2.0.1':
    '@hapi/validate': private
  '@hapi/vise@5.0.1':
    '@hapi/vise': private
  '@hapi/wreck@18.1.0':
    '@hapi/wreck': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': private
  '@prisma/config@6.10.1':
    '@prisma/config': private
  '@prisma/debug@6.10.1':
    '@prisma/debug': private
  '@prisma/engines-version@6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c':
    '@prisma/engines-version': private
  '@prisma/engines@6.10.1':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.10.1':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.10.1':
    '@prisma/get-platform': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bson@6.10.4:
    bson: private
  chokidar@3.6.0:
    chokidar: private
  concat-map@0.0.1:
    concat-map: private
  create-require@1.1.1:
    create-require: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  diff@4.0.2:
    diff: private
  fill-range@7.1.1:
    fill-range: private
  fsevents@2.3.3:
    fsevents: private
  glob-parent@5.1.2:
    glob-parent: private
  has-flag@3.0.0:
    has-flag: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  jiti@2.4.2:
    jiti: private
  kareem@2.6.3:
    kareem: private
  make-error@1.3.6:
    make-error: private
  memory-pager@1.5.0:
    memory-pager: private
  mime-db@1.54.0:
    mime-db: private
  minimatch@3.1.2:
    minimatch: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  mongodb@6.17.0:
    mongodb: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  normalize-path@3.0.0:
    normalize-path: private
  pg-cloudflare@1.2.6:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.1):
    pg-pool: private
  pg-protocol@1.10.1:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picomatch@2.3.1:
    picomatch: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  pstree.remy@1.1.8:
    pstree.remy: private
  punycode@2.3.1:
    punycode: private
  readdirp@3.6.0:
    readdirp: private
  semver@7.7.2:
    semver: private
  sift@17.1.3:
    sift: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  split2@4.2.0:
    split2: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  tr46@5.1.1:
    tr46: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.8.0:
    undici-types: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@14.2.0:
    whatwg-url: private
  xtend@4.0.2:
    xtend: private
  yn@3.1.1:
    yn: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.1
pendingBuilds: []
prunedAt: Thu, 19 Jun 2025 10:25:47 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
