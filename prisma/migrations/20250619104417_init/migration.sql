-- CreateTable
CREATE TABLE "user_incidents" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "incidentType" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "assignedTo" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "resolvedAt" TIMESTAMP(3),

    CONSTRAINT "user_incidents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "crosscore_requests" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "verificationType" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestData" JSONB NOT NULL,
    "responseData" JSONB,
    "experianRequestId" TEXT,
    "experianStatus" TEXT,
    "errorMessage" TEXT,
    "webhookReceived" BOOLEAN NOT NULL DEFAULT false,
    "webhookData" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "crosscore_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_incidents_userId_status_idx" ON "user_incidents"("userId", "status");

-- CreateIndex
CREATE INDEX "user_incidents_createdAt_idx" ON "user_incidents"("createdAt");

-- CreateIndex
CREATE INDEX "user_incidents_status_priority_idx" ON "user_incidents"("status", "priority");

-- CreateIndex
CREATE UNIQUE INDEX "crosscore_requests_requestId_key" ON "crosscore_requests"("requestId");

-- CreateIndex
CREATE INDEX "crosscore_requests_userId_status_idx" ON "crosscore_requests"("userId", "status");

-- CreateIndex
CREATE INDEX "crosscore_requests_createdAt_idx" ON "crosscore_requests"("createdAt");

-- CreateIndex
CREATE INDEX "crosscore_requests_status_verificationType_idx" ON "crosscore_requests"("status", "verificationType");

-- CreateIndex
CREATE INDEX "crosscore_requests_experianRequestId_idx" ON "crosscore_requests"("experianRequestId");
