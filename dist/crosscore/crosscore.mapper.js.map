{"version": 3, "file": "crosscore.mapper.js", "sourceRoot": "", "sources": ["../../src/crosscore/crosscore.mapper.ts"], "names": [], "mappings": ";;;AAAA,+BAAoC;AASpC,MAAa,eAAe;IAI1B,MAAM,CAAC,QAAQ,CACb,GAA2B;QAE3B,OAAO;YACL,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS,EAAE,IAAA,SAAM,GAAE;YACnB,gBAAgB,EAAE,GAAG,CAAC,gBAAgB;YACtC,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,MAAM,EAAE,SAAS;YACjB,eAAe,EAAE,KAAK;SACvB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc,CACnB,GAA2B;QAE3B,MAAM,UAAU,GAAuC,EAAE,CAAC;QAE1D,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC7B,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;YAE/B,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9D,UAAU,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YACtC,CAAC;QACH,CAAC;QACD,IAAI,GAAG,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACnC,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC;QAC7C,CAAC;QACD,IAAI,GAAG,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACxC,UAAU,CAAC,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,CAAC;QACvD,CAAC;QACD,IAAI,GAAG,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACrC,UAAU,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC;QACjD,CAAC;QACD,IAAI,GAAG,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACnC,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC;QAC7C,CAAC;QACD,IAAI,GAAG,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACtC,UAAU,CAAC,eAAe,GAAG,GAAG,CAAC,eAAe,CAAC;QACnD,CAAC;QACD,IAAI,GAAG,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAClC,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;QAC3C,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,MAAwB;QACxC,OAAO;YACL,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,MAAM,EAAE,MAAM,CAAC,MAKA;YACf,WAAW,EAAE,MAAM,CAAC,WAAkC;YACtD,YAAY,EAAE,MAAM,CAAC,YAA+C;YACpE,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,IAAI,SAAS;YACxD,cAAc,EAAE,MAAM,CAAC,cAAc,IAAI,SAAS;YAClD,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,SAAS;YAC9C,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,WAAW,EAAE,MAAM,CAAC,WAA8C;YAClE,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACzC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YACzC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE;SAC/C,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,QAA4B;QACjD,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,CAAC;IAKD,MAAM,CAAC,mBAAmB,CACxB,QAA4B,EAC5B,IAAY,EACZ,KAAa,EACb,KAAa;QAEb,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YACxC,UAAU,EAAE;gBACV,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;SACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,qBAAqB,CAAC,WAK5B;QACC,OAAO;YACL,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;YAChD,cAAc,EAAE,WAAW,CAAC,MAAM;YAClC,eAAe,EAAE,IAAI;YACrB,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,YAAY,EAAE,WAAW,CAAC,IAAI;YAC9B,MAAM,EAAE,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,MAAM,CAAC;YAClE,WAAW,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CACpD,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE,CACjC;gBACC,CAAC,CAAC,IAAI,IAAI,EAAE;gBACZ,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,iCAAiC,CAC9C,cAAsB;QAEtB,MAAM,MAAM,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC;QAC5C,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,SAAS,CAAC;YACf,KAAK,WAAW;gBACd,OAAO,SAAS,CAAC;YACnB,KAAK,YAAY,CAAC;YAClB,KAAK,aAAa;gBAChB,OAAO,aAAa,CAAC;YACvB,KAAK,WAAW,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC;YACrB,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACV,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,UAAU;gBACb,OAAO,WAAW,CAAC;YACrB;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;CACF;AAhKD,0CAgKC"}