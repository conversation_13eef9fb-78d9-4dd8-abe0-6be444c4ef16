"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrosscoreService = void 0;
const crosscore_repository_1 = require("./crosscore.repository");
const crosscore_mapper_1 = require("./crosscore.mapper");
const utils_1 = require("../common/utils");
class CrosscoreService {
    constructor() {
        this.repository = new crosscore_repository_1.CrosscoreRepository();
    }
    async createRequest(request) {
        try {
            const entityData = crosscore_mapper_1.CrosscoreMapper.toEntity(request);
            const crosscoreRequest = await this.repository.create(entityData);
            await this.repository.updateById(crosscoreRequest.id, {
                status: 'in_progress',
            });
            const updatedRequest = await this.repository.findById(crosscoreRequest.id);
            return crosscore_mapper_1.CrosscoreMapper.toResponse(updatedRequest);
        }
        catch (error) {
            throw new Error(`Failed to create crosscore request: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getRequestById(id) {
        try {
            const request = await this.repository.findById(id);
            if (!request) {
                return null;
            }
            return crosscore_mapper_1.CrosscoreMapper.toResponse(request);
        }
        catch (error) {
            throw new Error(`Failed to get crosscore request: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getRequestByRequestId(requestId) {
        try {
            const request = await this.repository.findByRequestId(requestId);
            if (!request) {
                return null;
            }
            return crosscore_mapper_1.CrosscoreMapper.toResponse(request);
        }
        catch (error) {
            throw new Error(`Failed to get crosscore request: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getRequests(query) {
        try {
            const { requests, total } = await this.repository.findMany(query);
            const page = query.page || 1;
            const limit = query.limit || 10;
            return crosscore_mapper_1.CrosscoreMapper.toPaginatedResponse(requests, page, limit, total);
        }
        catch (error) {
            throw new Error(`Failed to get crosscore requests: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getRequestsByUserId(userId) {
        try {
            const requests = await this.repository.findByUserId(userId);
            return crosscore_mapper_1.CrosscoreMapper.toResponseArray(requests);
        }
        catch (error) {
            throw new Error(`Failed to get user requests: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async updateRequest(id, request) {
        try {
            const updateData = crosscore_mapper_1.CrosscoreMapper.toUpdateEntity(request);
            const updatedRequest = await this.repository.updateById(id, updateData);
            if (!updatedRequest) {
                return null;
            }
            return crosscore_mapper_1.CrosscoreMapper.toResponse(updatedRequest);
        }
        catch (error) {
            throw new Error(`Failed to update crosscore request: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async cancelRequest(id) {
        try {
            const updateData = {
                status: 'cancelled',
                completedAt: new Date(),
            };
            const request = await this.repository.updateById(id, updateData);
            if (!request) {
                return null;
            }
            return crosscore_mapper_1.CrosscoreMapper.toResponse(request);
        }
        catch (error) {
            throw new Error(`Failed to cancel crosscore request: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async processWebhook(webhookPayload) {
        try {
            let request = await this.repository.findByRequestId(webhookPayload.requestId);
            if (!request && webhookPayload.experianRequestId) {
                request = await this.repository.findByExperianRequestId(webhookPayload.experianRequestId);
            }
            if (!request) {
                throw new Error(`Request not found for webhook: ${webhookPayload.requestId}`);
            }
            const updateData = crosscore_mapper_1.CrosscoreMapper.webhookToUpdateEntity({
                requestId: webhookPayload.requestId,
                experianRequestId: webhookPayload.experianRequestId,
                status: webhookPayload.status,
                data: webhookPayload.data,
            });
            const updatedRequest = await this.repository.updateById(request.id, updateData);
            if (!updatedRequest) {
                throw new Error('Failed to update request with webhook data');
            }
            return crosscore_mapper_1.CrosscoreMapper.toResponse(updatedRequest);
        }
        catch (error) {
            throw new Error(`Failed to process webhook: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getPendingWebhooks() {
        try {
            const requests = await this.repository.findPendingWebhooks();
            return crosscore_mapper_1.CrosscoreMapper.toResponseArray(requests);
        }
        catch (error) {
            throw new Error(`Failed to get pending webhooks: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async getStatistics() {
        try {
            return await this.repository.getStatistics();
        }
        catch (error) {
            throw new Error(`Failed to get statistics: ${(0, utils_1.getErrorMessage)(error)}`);
        }
    }
    async callExperianAPI(verificationType, requestData) {
        return {
            experianRequestId: `EXP_${Date.now()}`,
            status: 'submitted',
        };
    }
}
exports.CrosscoreService = CrosscoreService;
//# sourceMappingURL=crosscore.service.js.map