import { CrosscoreRequest, Prisma } from '../generated/prisma';
import { CreateCrosscoreRequest, UpdateCrosscoreRequest, CrosscoreResponse, PaginatedCrosscoreResponse } from './crosscore.dto';
export declare class CrosscoreMapper {
    static toEntity(dto: CreateCrosscoreRequest): Prisma.CrosscoreRequestCreateInput;
    static toUpdateEntity(dto: UpdateCrosscoreRequest): Prisma.CrosscoreRequestUpdateInput;
    static toResponse(entity: CrosscoreRequest): CrosscoreResponse;
    static toResponseArray(entities: CrosscoreRequest[]): CrosscoreResponse[];
    static toPaginatedResponse(entities: CrosscoreRequest[], page: number, limit: number, total: number): PaginatedCrosscoreResponse;
    static webhookToUpdateEntity(webhookData: {
        requestId: string;
        experianRequestId: string;
        status: string;
        data: Record<string, any>;
    }): Prisma.CrosscoreRequestUpdateInput;
    private static mapExperianStatusToInternalStatus;
}
//# sourceMappingURL=crosscore.mapper.d.ts.map