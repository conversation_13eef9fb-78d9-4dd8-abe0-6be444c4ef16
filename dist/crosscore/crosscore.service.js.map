{"version": 3, "file": "crosscore.service.js", "sourceRoot": "", "sources": ["../../src/crosscore/crosscore.service.ts"], "names": [], "mappings": ";;;AAAA,iEAA6D;AAC7D,yDAAqD;AACrD,2CAAkD;AAUlD,MAAa,gBAAgB;IAG3B;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,0CAAmB,EAAE,CAAC;IAC9C,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,OAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,kCAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAIlE,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE;gBACpD,MAAM,EAAE,aAAa;aACtB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CACnD,gBAAgB,CAAC,EAAE,CACpB,CAAC;YACF,OAAO,kCAAe,CAAC,UAAU,CAAC,cAAe,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,uCAAuC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,EAAU;QAC7B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,kCAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,oCAAoC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAC7D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,kCAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,oCAAoC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAC7D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CACf,KAAwB;QAExB,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;YAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YAEhC,OAAO,kCAAe,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,qCAAqC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC5D,OAAO,kCAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CACjB,EAAU,EACV,OAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,kCAAe,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YACxE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,kCAAe,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,uCAAuC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG;gBACjB,MAAM,EAAE,WAAoB;gBAC5B,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,kCAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,uCAAuC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAChE,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAClB,cAAuC;QAEvC,IAAI,CAAC;YAEH,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CACjD,cAAc,CAAC,SAAS,CACzB,CAAC;YAEF,IAAI,CAAC,OAAO,IAAI,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACjD,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,uBAAuB,CACrD,cAAc,CAAC,iBAAiB,CACjC,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CACb,kCAAkC,cAAc,CAAC,SAAS,EAAE,CAC7D,CAAC;YACJ,CAAC;YAGD,MAAM,UAAU,GAAG,kCAAe,CAAC,qBAAqB,CAAC;gBACvD,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;gBACnD,MAAM,EAAE,cAAc,CAAC,MAAM;gBAC7B,IAAI,EAAE,cAAc,CAAC,IAAI;aAC1B,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CACrD,OAAO,CAAC,EAAE,EACV,UAAU,CACX,CAAC;YAEF,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,kCAAe,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;YAC7D,OAAO,kCAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb,mCAAmC,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAC5D,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa;QASjB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAC3B,gBAAwB,EACxB,WAAgC;QAIhC,OAAO;YACL,iBAAiB,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACtC,MAAM,EAAE,WAAW;SACpB,CAAC;IACJ,CAAC;CACF;AA7OD,4CA6OC"}