"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrosscoreMapper = void 0;
const uuid_1 = require("uuid");
class CrosscoreMapper {
    static toEntity(dto) {
        return {
            userId: dto.userId,
            requestId: (0, uuid_1.v4)(),
            verificationType: dto.verificationType,
            requestData: dto.requestData,
            status: 'pending',
            webhookReceived: false,
        };
    }
    static toUpdateEntity(dto) {
        const updateData = {};
        if (dto.status !== undefined) {
            updateData.status = dto.status;
            if (['completed', 'failed', 'cancelled'].includes(dto.status)) {
                updateData.completedAt = new Date();
            }
        }
        if (dto.responseData !== undefined) {
            updateData.responseData = dto.responseData;
        }
        if (dto.experianRequestId !== undefined) {
            updateData.experianRequestId = dto.experianRequestId;
        }
        if (dto.experianStatus !== undefined) {
            updateData.experianStatus = dto.experianStatus;
        }
        if (dto.errorMessage !== undefined) {
            updateData.errorMessage = dto.errorMessage;
        }
        if (dto.webhookReceived !== undefined) {
            updateData.webhookReceived = dto.webhookReceived;
        }
        if (dto.webhookData !== undefined) {
            updateData.webhookData = dto.webhookData;
        }
        return updateData;
    }
    static toResponse(entity) {
        return {
            id: entity.id,
            userId: entity.userId,
            requestId: entity.requestId,
            verificationType: entity.verificationType,
            status: entity.status,
            requestData: entity.requestData,
            responseData: entity.responseData,
            experianRequestId: entity.experianRequestId || undefined,
            experianStatus: entity.experianStatus || undefined,
            errorMessage: entity.errorMessage || undefined,
            webhookReceived: entity.webhookReceived,
            webhookData: entity.webhookData,
            createdAt: entity.createdAt.toISOString(),
            updatedAt: entity.updatedAt.toISOString(),
            completedAt: entity.completedAt?.toISOString(),
        };
    }
    static toResponseArray(entities) {
        return entities.map(entity => this.toResponse(entity));
    }
    static toPaginatedResponse(entities, page, limit, total) {
        return {
            requests: this.toResponseArray(entities),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    static webhookToUpdateEntity(webhookData) {
        return {
            experianRequestId: webhookData.experianRequestId,
            experianStatus: webhookData.status,
            webhookReceived: true,
            webhookData: webhookData.data,
            responseData: webhookData.data,
            status: this.mapExperianStatusToInternalStatus(webhookData.status),
            completedAt: ['completed', 'failed', 'error'].includes(webhookData.status.toLowerCase())
                ? new Date()
                : undefined,
        };
    }
    static mapExperianStatusToInternalStatus(experianStatus) {
        const status = experianStatus.toLowerCase();
        switch (status) {
            case 'pending':
            case 'submitted':
                return 'pending';
            case 'processing':
            case 'in_progress':
                return 'in_progress';
            case 'completed':
            case 'success':
                return 'completed';
            case 'failed':
            case 'error':
                return 'failed';
            case 'cancelled':
            case 'canceled':
                return 'cancelled';
            default:
                return 'pending';
        }
    }
}
exports.CrosscoreMapper = CrosscoreMapper;
//# sourceMappingURL=crosscore.mapper.js.map