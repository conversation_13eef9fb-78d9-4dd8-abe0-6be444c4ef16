import { CrosscoreRequest, Prisma } from '../generated/prisma';
import { GetCrosscoreQuery } from './crosscore.dto';
export declare class CrosscoreRepository {
    private prisma;
    create(requestData: Prisma.CrosscoreRequestCreateInput): Promise<CrosscoreRequest>;
    findById(id: string): Promise<CrosscoreRequest | null>;
    findByRequestId(requestId: string): Promise<CrosscoreRequest | null>;
    findByExperianRequestId(experianRequestId: string): Promise<CrosscoreRequest | null>;
    findMany(query: GetCrosscoreQuery): Promise<{
        requests: CrosscoreRequest[];
        total: number;
    }>;
    findByUserId(userId: string): Promise<CrosscoreRequest[]>;
    updateById(id: string, updateData: Prisma.CrosscoreRequestUpdateInput): Promise<CrosscoreRequest | null>;
    updateByRequestId(requestId: string, updateData: Prisma.CrosscoreRequestUpdateInput): Promise<CrosscoreRequest | null>;
    updateByExperianRequestId(experianRequestId: string, updateData: Prisma.CrosscoreRequestUpdateInput): Promise<CrosscoreRequest | null>;
    deleteById(id: string): Promise<CrosscoreRequest | null>;
    findByStatus(status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'): Promise<CrosscoreRequest[]>;
    findPendingWebhooks(): Promise<CrosscoreRequest[]>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byVerificationType: Record<string, number>;
        webhookStats: {
            received: number;
            pending: number;
        };
    }>;
}
//# sourceMappingURL=crosscore.repository.d.ts.map