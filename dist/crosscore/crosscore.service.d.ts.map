{"version": 3, "file": "crosscore.service.d.ts", "sourceRoot": "", "sources": ["../../src/crosscore/crosscore.service.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,sBAAsB,EACtB,sBAAsB,EACtB,iBAAiB,EACjB,iBAAiB,EACjB,0BAA0B,EAC1B,uBAAuB,EACxB,MAAM,iBAAiB,CAAC;AAEzB,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,UAAU,CAAsB;;IASlC,aAAa,CACjB,OAAO,EAAE,sBAAsB,GAC9B,OAAO,CAAC,iBAAiB,CAAC;IAyBvB,cAAc,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAiB7D,qBAAqB,CACzB,SAAS,EAAE,MAAM,GAChB,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAiB9B,WAAW,CACf,KAAK,EAAE,iBAAiB,GACvB,OAAO,CAAC,0BAA0B,CAAC;IAiBhC,mBAAmB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAYjE,aAAa,CACjB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,sBAAsB,GAC9B,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAkB9B,aAAa,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAqB5D,cAAc,CAClB,cAAc,EAAE,uBAAuB,GACtC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IA6C9B,kBAAkB,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;IAclD,aAAa,IAAI,OAAO,CAAC;QAC7B,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,kBAAkB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,YAAY,EAAE;YACZ,QAAQ,EAAE,MAAM,CAAC;YACjB,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;KACH,CAAC;YAWY,eAAe;CAW9B"}