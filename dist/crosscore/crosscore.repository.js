"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrosscoreRepository = void 0;
const prisma_1 = require("../generated/prisma");
const database_1 = require("../common/utils/database");
class CrosscoreRepository {
    constructor() {
        this.prisma = database_1.DatabaseConnection.getInstance().getPrismaClient();
    }
    async create(requestData) {
        return await this.prisma.crosscoreRequest.create({
            data: requestData,
        });
    }
    async findById(id) {
        return await this.prisma.crosscoreRequest.findUnique({
            where: { id },
        });
    }
    async findByRequestId(requestId) {
        return await this.prisma.crosscoreRequest.findUnique({
            where: { requestId },
        });
    }
    async findByExperianRequestId(experianRequestId) {
        return await this.prisma.crosscoreRequest.findFirst({
            where: { experianRequestId },
        });
    }
    async findMany(query) {
        const where = {};
        if (query.userId) {
            where.userId = query.userId;
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.verificationType) {
            where.verificationType = query.verificationType;
        }
        if (query.experianRequestId) {
            where.experianRequestId = query.experianRequestId;
        }
        if (query.webhookReceived !== undefined) {
            where.webhookReceived = query.webhookReceived;
        }
        if (query.dateFrom || query.dateTo) {
            where.createdAt = {};
            if (query.dateFrom) {
                where.createdAt.gte = new Date(query.dateFrom);
            }
            if (query.dateTo) {
                where.createdAt.lte = new Date(query.dateTo);
            }
        }
        const sortField = query.sortBy || 'createdAt';
        const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
        const orderBy = {
            [sortField]: sortOrder,
        };
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;
        const [requests, total] = await Promise.all([
            this.prisma.crosscoreRequest.findMany({
                where,
                orderBy,
                skip,
                take: limit,
            }),
            this.prisma.crosscoreRequest.count({ where }),
        ]);
        return { requests, total };
    }
    async findByUserId(userId) {
        return await this.prisma.crosscoreRequest.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
        });
    }
    async updateById(id, updateData) {
        try {
            return await this.prisma.crosscoreRequest.update({
                where: { id },
                data: updateData,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError &&
                error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
    async updateByRequestId(requestId, updateData) {
        try {
            return await this.prisma.crosscoreRequest.update({
                where: { requestId },
                data: updateData,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError &&
                error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
    async updateByExperianRequestId(experianRequestId, updateData) {
        const existingRequest = await this.prisma.crosscoreRequest.findFirst({
            where: { experianRequestId },
        });
        if (!existingRequest) {
            return null;
        }
        return await this.prisma.crosscoreRequest.update({
            where: { id: existingRequest.id },
            data: updateData,
        });
    }
    async deleteById(id) {
        try {
            return await this.prisma.crosscoreRequest.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError &&
                error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
    async findByStatus(status) {
        return await this.prisma.crosscoreRequest.findMany({
            where: { status },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findPendingWebhooks() {
        return await this.prisma.crosscoreRequest.findMany({
            where: {
                webhookReceived: false,
                status: { in: ['pending', 'in_progress'] },
            },
            orderBy: { createdAt: 'asc' },
        });
    }
    async getStatistics() {
        const [total, statusStats, typeStats, webhookStats] = await Promise.all([
            this.prisma.crosscoreRequest.count(),
            this.prisma.crosscoreRequest.groupBy({
                by: ['status'],
                _count: { status: true },
            }),
            this.prisma.crosscoreRequest.groupBy({
                by: ['verificationType'],
                _count: { verificationType: true },
            }),
            this.prisma.crosscoreRequest.groupBy({
                by: ['webhookReceived'],
                _count: { webhookReceived: true },
            }),
        ]);
        const byStatus = {};
        statusStats.forEach((stat) => {
            byStatus[stat.status] = stat._count.status;
        });
        const byVerificationType = {};
        typeStats.forEach((stat) => {
            byVerificationType[stat.verificationType] = stat._count.verificationType;
        });
        const webhookStatsObj = {
            received: 0,
            pending: 0,
        };
        webhookStats.forEach((stat) => {
            if (stat.webhookReceived === true) {
                webhookStatsObj.received = stat._count.webhookReceived;
            }
            else {
                webhookStatsObj.pending = stat._count.webhookReceived;
            }
        });
        return {
            total,
            byStatus,
            byVerificationType,
            webhookStats: webhookStatsObj,
        };
    }
}
exports.CrosscoreRepository = CrosscoreRepository;
//# sourceMappingURL=crosscore.repository.js.map