import { UserIncident, Prisma } from '../generated/prisma';
import { GetUserIncidentsQuery } from './userIncident.dto';
export declare class UserIncidentRepository {
    private prisma;
    create(incidentData: Prisma.UserIncidentCreateInput): Promise<UserIncident>;
    findById(id: string): Promise<UserIncident | null>;
    findMany(query: GetUserIncidentsQuery): Promise<{
        incidents: UserIncident[];
        total: number;
    }>;
    findByUserId(userId: string): Promise<UserIncident[]>;
    updateById(id: string, updateData: Prisma.UserIncidentUpdateInput): Promise<UserIncident | null>;
    deleteById(id: string): Promise<UserIncident | null>;
    findByStatus(status: 'pending' | 'in_progress' | 'resolved' | 'closed'): Promise<UserIncident[]>;
    findByAssignedTo(assignedTo: string): Promise<UserIncident[]>;
    getStatistics(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byPriority: Record<string, number>;
    }>;
}
//# sourceMappingURL=userIncident.repository.d.ts.map