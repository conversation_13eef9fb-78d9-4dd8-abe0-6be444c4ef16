"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserIncidentMapper = void 0;
class UserIncidentMapper {
    static toEntity(dto) {
        return {
            userId: dto.userId,
            incidentType: dto.incidentType,
            description: dto.description,
            priority: dto.priority || 'medium',
            metadata: dto.metadata || undefined,
            status: 'pending',
        };
    }
    static toUpdateEntity(dto) {
        const updateData = {};
        if (dto.incidentType !== undefined) {
            updateData.incidentType = dto.incidentType;
        }
        if (dto.description !== undefined) {
            updateData.description = dto.description;
        }
        if (dto.status !== undefined) {
            updateData.status = dto.status;
            if (dto.status === 'resolved' || dto.status === 'closed') {
                updateData.resolvedAt = new Date();
            }
        }
        if (dto.priority !== undefined) {
            updateData.priority = dto.priority;
        }
        if (dto.assignedTo !== undefined) {
            updateData.assignedTo = dto.assignedTo || null;
        }
        if (dto.metadata !== undefined) {
            updateData.metadata = dto.metadata;
        }
        return updateData;
    }
    static toResponse(entity) {
        return {
            id: entity.id,
            userId: entity.userId,
            incidentType: entity.incidentType,
            description: entity.description,
            status: entity.status,
            priority: entity.priority,
            assignedTo: entity.assignedTo || undefined,
            metadata: entity.metadata,
            createdAt: entity.createdAt.toISOString(),
            updatedAt: entity.updatedAt.toISOString(),
            resolvedAt: entity.resolvedAt?.toISOString(),
        };
    }
    static toResponseArray(entities) {
        return entities.map(entity => this.toResponse(entity));
    }
    static toPaginatedResponse(entities, page, limit, total) {
        return {
            incidents: this.toResponseArray(entities),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
}
exports.UserIncidentMapper = UserIncidentMapper;
//# sourceMappingURL=userIncident.mapper.js.map