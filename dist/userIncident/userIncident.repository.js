"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserIncidentRepository = void 0;
const prisma_1 = require("../generated/prisma");
const database_1 = require("../common/utils/database");
class UserIncidentRepository {
    constructor() {
        this.prisma = database_1.DatabaseConnection.getInstance().getPrismaClient();
    }
    async create(incidentData) {
        return await this.prisma.userIncident.create({
            data: incidentData,
        });
    }
    async findById(id) {
        return await this.prisma.userIncident.findUnique({
            where: { id },
        });
    }
    async findMany(query) {
        const where = {};
        if (query.userId) {
            where.userId = query.userId;
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.priority) {
            where.priority = query.priority;
        }
        if (query.incidentType) {
            where.incidentType = query.incidentType;
        }
        if (query.assignedTo) {
            where.assignedTo = query.assignedTo;
        }
        const sortField = query.sortBy || 'createdAt';
        const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';
        const orderBy = {
            [sortField]: sortOrder,
        };
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;
        const [incidents, total] = await Promise.all([
            this.prisma.userIncident.findMany({
                where,
                orderBy,
                skip,
                take: limit,
            }),
            this.prisma.userIncident.count({ where }),
        ]);
        return { incidents, total };
    }
    async findByUserId(userId) {
        return await this.prisma.userIncident.findMany({
            where: { userId },
            orderBy: { createdAt: 'desc' },
        });
    }
    async updateById(id, updateData) {
        try {
            return await this.prisma.userIncident.update({
                where: { id },
                data: updateData,
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError &&
                error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
    async deleteById(id) {
        try {
            return await this.prisma.userIncident.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error instanceof prisma_1.Prisma.PrismaClientKnownRequestError &&
                error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
    async findByStatus(status) {
        return await this.prisma.userIncident.findMany({
            where: { status },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findByAssignedTo(assignedTo) {
        return await this.prisma.userIncident.findMany({
            where: { assignedTo },
            orderBy: [{ priority: 'desc' }, { createdAt: 'desc' }],
        });
    }
    async getStatistics() {
        const [total, statusStats, priorityStats] = await Promise.all([
            this.prisma.userIncident.count(),
            this.prisma.userIncident.groupBy({
                by: ['status'],
                _count: { status: true },
            }),
            this.prisma.userIncident.groupBy({
                by: ['priority'],
                _count: { priority: true },
            }),
        ]);
        const byStatus = {};
        statusStats.forEach((stat) => {
            byStatus[stat.status] = stat._count.status;
        });
        const byPriority = {};
        priorityStats.forEach((stat) => {
            byPriority[stat.priority] = stat._count.priority;
        });
        return { total, byStatus, byPriority };
    }
}
exports.UserIncidentRepository = UserIncidentRepository;
//# sourceMappingURL=userIncident.repository.js.map