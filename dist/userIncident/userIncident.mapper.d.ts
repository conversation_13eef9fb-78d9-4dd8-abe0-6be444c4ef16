import { UserIncident, Prisma } from '../generated/prisma';
import { CreateUserIncidentRequest, UpdateUserIncidentRequest, UserIncidentResponse, PaginatedUserIncidentsResponse } from './userIncident.dto';
export declare class UserIncidentMapper {
    static toEntity(dto: CreateUserIncidentRequest): Prisma.UserIncidentCreateInput;
    static toUpdateEntity(dto: UpdateUserIncidentRequest): Prisma.UserIncidentUpdateInput;
    static toResponse(entity: UserIncident): UserIncidentResponse;
    static toResponseArray(entities: UserIncident[]): UserIncidentResponse[];
    static toPaginatedResponse(entities: UserIncident[], page: number, limit: number, total: number): PaginatedUserIncidentsResponse;
}
//# sourceMappingURL=userIncident.mapper.d.ts.map