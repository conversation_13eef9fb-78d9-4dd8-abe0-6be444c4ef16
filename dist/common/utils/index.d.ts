import { PaginationMeta } from '../dtos';
export declare function getErrorMessage(error: unknown): string;
export declare function createPaginationMeta(page: number, limit: number, total: number): PaginationMeta;
export declare function normalizePaginationParams(page?: number, limit?: number): {
    page: number;
    limit: number;
};
export declare function calculateSkip(page: number, limit: number): number;
export declare function generateRandomString(length: number): string;
export declare function generateRequestId(prefix?: string): string;
export declare function isValidEmail(email: string): boolean;
export declare function isValidUUID(uuid: string): boolean;
export declare function sanitizeString(input: string): string;
export declare function truncateText(text: string, maxLength: number): string;
export declare function toTitleCase(str: string): string;
export declare function toSnakeCase(str: string): string;
export declare function toCamelCase(str: string): string;
export declare function deepClone<T>(obj: T): T;
export declare function removeNullish<T extends Record<string, any>>(obj: T): Partial<T>;
export declare function delay(ms: number): Promise<void>;
export declare function retryWithBackoff<T>(fn: () => Promise<T>, maxRetries?: number, baseDelay?: number): Promise<T>;
export declare function formatDateToISO(date: Date): string;
export declare function parseISODate(dateString: string): Date;
export declare function isDateInRange(date: Date, startDate: Date, endDate: Date): boolean;
export declare function maskSensitiveData(data: string, visibleChars?: number, maskChar?: string): string;
export * from './database';
//# sourceMappingURL=index.d.ts.map