import { PrismaClient } from '../../generated/prisma';
export declare class DatabaseConnection {
    private static instance;
    private prisma;
    private isConnected;
    private constructor();
    static getInstance(): DatabaseConnection;
    getPrismaClient(): PrismaClient;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getConnectionStatus(): boolean;
    getConnectionInfo(): {
        isConnected: boolean;
        databaseUrl?: string;
    };
    healthCheck(): Promise<{
        status: 'healthy' | 'unhealthy';
        responseTime: number;
        error?: string;
    }>;
}
//# sourceMappingURL=database.d.ts.map