"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConnection = void 0;
const prisma_1 = require("../../generated/prisma");
class DatabaseConnection {
    constructor() {
        this.isConnected = false;
        this.prisma = new prisma_1.PrismaClient({
            log: ['query', 'info', 'warn', 'error'],
        });
    }
    static getInstance() {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection();
        }
        return DatabaseConnection.instance;
    }
    getPrismaClient() {
        return this.prisma;
    }
    async connect() {
        try {
            if (this.isConnected) {
                console.log('Database already connected');
                return;
            }
            await this.prisma.$connect();
            this.isConnected = true;
            console.log('Connected to PostgreSQL successfully');
        }
        catch (error) {
            console.error('Failed to connect to PostgreSQL:', error);
            this.isConnected = false;
            throw error;
        }
    }
    async disconnect() {
        try {
            if (!this.isConnected) {
                console.log('Database not connected');
                return;
            }
            await this.prisma.$disconnect();
            this.isConnected = false;
            console.log('Disconnected from PostgreSQL');
        }
        catch (error) {
            console.error('Error disconnecting from PostgreSQL:', error);
            throw error;
        }
    }
    getConnectionStatus() {
        return this.isConnected;
    }
    getConnectionInfo() {
        return {
            isConnected: this.isConnected,
            databaseUrl: process.env.DATABASE_URL ? '[REDACTED]' : undefined,
        };
    }
    async healthCheck() {
        const startTime = Date.now();
        try {
            if (!this.isConnected) {
                return {
                    status: 'unhealthy',
                    responseTime: Date.now() - startTime,
                    error: 'Database not connected',
                };
            }
            await this.prisma.$queryRaw `SELECT 1`;
            return {
                status: 'healthy',
                responseTime: Date.now() - startTime,
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
}
exports.DatabaseConnection = DatabaseConnection;
//# sourceMappingURL=database.js.map