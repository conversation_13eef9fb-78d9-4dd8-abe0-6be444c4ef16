"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getErrorMessage = getErrorMessage;
exports.createPaginationMeta = createPaginationMeta;
exports.normalizePaginationParams = normalizePaginationParams;
exports.calculateSkip = calculateSkip;
exports.generateRandomString = generateRandomString;
exports.generateRequestId = generateRequestId;
exports.isValidEmail = isValidEmail;
exports.isValidUUID = isValidUUID;
exports.sanitizeString = sanitizeString;
exports.truncateText = truncateText;
exports.toTitleCase = toTitleCase;
exports.toSnakeCase = toSnakeCase;
exports.toCamelCase = toCamelCase;
exports.deepClone = deepClone;
exports.removeNullish = removeNullish;
exports.delay = delay;
exports.retryWithBackoff = retryWithBackoff;
exports.formatDateToISO = formatDateToISO;
exports.parseISODate = parseISODate;
exports.isDateInRange = isDateInRange;
exports.maskSensitiveData = maskSensitiveData;
const constants_1 = require("../constants");
function getErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'Unknown error occurred';
}
function createPaginationMeta(page, limit, total) {
    const totalPages = Math.ceil(total / limit);
    return {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
    };
}
function normalizePaginationParams(page, limit) {
    const normalizedPage = Math.max(1, page || constants_1.PAGINATION_CONSTANTS.DEFAULT_PAGE);
    const normalizedLimit = Math.min(constants_1.PAGINATION_CONSTANTS.MAX_LIMIT, Math.max(constants_1.PAGINATION_CONSTANTS.MIN_LIMIT, limit || constants_1.PAGINATION_CONSTANTS.DEFAULT_LIMIT));
    return {
        page: normalizedPage,
        limit: normalizedLimit,
    };
}
function calculateSkip(page, limit) {
    return (page - 1) * limit;
}
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
function generateRequestId(prefix) {
    const timestamp = Date.now().toString(36);
    const random = generateRandomString(8);
    return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}
function sanitizeString(input) {
    return input.replace(/[<>\"'&]/g, '');
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 3) + '...';
}
function toTitleCase(str) {
    return str.replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
}
function toSnakeCase(str) {
    return str
        .replace(/\W+/g, ' ')
        .split(/ |\B(?=[A-Z])/)
        .map(word => word.toLowerCase())
        .join('_');
}
function toCamelCase(str) {
    return str
        .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index === 0 ? word.toLowerCase() : word.toUpperCase())
        .replace(/\s+/g, '');
}
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    return obj;
}
function removeNullish(obj) {
    const result = {};
    for (const key in obj) {
        if (obj[key] !== null && obj[key] !== undefined) {
            result[key] = obj[key];
        }
    }
    return result;
}
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {
    let lastError;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxRetries) {
                throw lastError;
            }
            const delayMs = baseDelay * Math.pow(2, attempt);
            await delay(delayMs);
        }
    }
    throw lastError;
}
function formatDateToISO(date) {
    return date.toISOString();
}
function parseISODate(dateString) {
    return new Date(dateString);
}
function isDateInRange(date, startDate, endDate) {
    return date >= startDate && date <= endDate;
}
function maskSensitiveData(data, visibleChars = 3, maskChar = '*') {
    if (data.length <= visibleChars * 2) {
        return maskChar.repeat(data.length);
    }
    const start = data.substring(0, visibleChars);
    const end = data.substring(data.length - visibleChars);
    const middle = maskChar.repeat(data.length - visibleChars * 2);
    return start + middle + end;
}
__exportStar(require("./database"), exports);
//# sourceMappingURL=index.js.map