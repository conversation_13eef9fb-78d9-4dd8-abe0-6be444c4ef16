
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model UserIncident
 * 
 */
export type UserIncident = $Result.DefaultSelection<Prisma.$UserIncidentPayload>
/**
 * Model CrosscoreRequest
 * 
 */
export type CrosscoreRequest = $Result.DefaultSelection<Prisma.$CrosscoreRequestPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more UserIncidents
 * const userIncidents = await prisma.userIncident.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more UserIncidents
   * const userIncidents = await prisma.userIncident.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.userIncident`: Exposes CRUD operations for the **UserIncident** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more UserIncidents
    * const userIncidents = await prisma.userIncident.findMany()
    * ```
    */
  get userIncident(): Prisma.UserIncidentDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.crosscoreRequest`: Exposes CRUD operations for the **CrosscoreRequest** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more CrosscoreRequests
    * const crosscoreRequests = await prisma.crosscoreRequest.findMany()
    * ```
    */
  get crosscoreRequest(): Prisma.CrosscoreRequestDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.10.1
   * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    UserIncident: 'UserIncident',
    CrosscoreRequest: 'CrosscoreRequest'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "userIncident" | "crosscoreRequest"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      UserIncident: {
        payload: Prisma.$UserIncidentPayload<ExtArgs>
        fields: Prisma.UserIncidentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserIncidentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserIncidentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          findFirst: {
            args: Prisma.UserIncidentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserIncidentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          findMany: {
            args: Prisma.UserIncidentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>[]
          }
          create: {
            args: Prisma.UserIncidentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          createMany: {
            args: Prisma.UserIncidentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserIncidentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>[]
          }
          delete: {
            args: Prisma.UserIncidentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          update: {
            args: Prisma.UserIncidentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          deleteMany: {
            args: Prisma.UserIncidentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserIncidentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserIncidentUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>[]
          }
          upsert: {
            args: Prisma.UserIncidentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserIncidentPayload>
          }
          aggregate: {
            args: Prisma.UserIncidentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUserIncident>
          }
          groupBy: {
            args: Prisma.UserIncidentGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserIncidentGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserIncidentCountArgs<ExtArgs>
            result: $Utils.Optional<UserIncidentCountAggregateOutputType> | number
          }
        }
      }
      CrosscoreRequest: {
        payload: Prisma.$CrosscoreRequestPayload<ExtArgs>
        fields: Prisma.CrosscoreRequestFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CrosscoreRequestFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CrosscoreRequestFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          findFirst: {
            args: Prisma.CrosscoreRequestFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CrosscoreRequestFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          findMany: {
            args: Prisma.CrosscoreRequestFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>[]
          }
          create: {
            args: Prisma.CrosscoreRequestCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          createMany: {
            args: Prisma.CrosscoreRequestCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CrosscoreRequestCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>[]
          }
          delete: {
            args: Prisma.CrosscoreRequestDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          update: {
            args: Prisma.CrosscoreRequestUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          deleteMany: {
            args: Prisma.CrosscoreRequestDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CrosscoreRequestUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.CrosscoreRequestUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>[]
          }
          upsert: {
            args: Prisma.CrosscoreRequestUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrosscoreRequestPayload>
          }
          aggregate: {
            args: Prisma.CrosscoreRequestAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCrosscoreRequest>
          }
          groupBy: {
            args: Prisma.CrosscoreRequestGroupByArgs<ExtArgs>
            result: $Utils.Optional<CrosscoreRequestGroupByOutputType>[]
          }
          count: {
            args: Prisma.CrosscoreRequestCountArgs<ExtArgs>
            result: $Utils.Optional<CrosscoreRequestCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    userIncident?: UserIncidentOmit
    crosscoreRequest?: CrosscoreRequestOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model UserIncident
   */

  export type AggregateUserIncident = {
    _count: UserIncidentCountAggregateOutputType | null
    _min: UserIncidentMinAggregateOutputType | null
    _max: UserIncidentMaxAggregateOutputType | null
  }

  export type UserIncidentMinAggregateOutputType = {
    id: string | null
    userId: string | null
    incidentType: string | null
    description: string | null
    status: string | null
    priority: string | null
    assignedTo: string | null
    createdAt: Date | null
    updatedAt: Date | null
    resolvedAt: Date | null
  }

  export type UserIncidentMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    incidentType: string | null
    description: string | null
    status: string | null
    priority: string | null
    assignedTo: string | null
    createdAt: Date | null
    updatedAt: Date | null
    resolvedAt: Date | null
  }

  export type UserIncidentCountAggregateOutputType = {
    id: number
    userId: number
    incidentType: number
    description: number
    status: number
    priority: number
    assignedTo: number
    metadata: number
    createdAt: number
    updatedAt: number
    resolvedAt: number
    _all: number
  }


  export type UserIncidentMinAggregateInputType = {
    id?: true
    userId?: true
    incidentType?: true
    description?: true
    status?: true
    priority?: true
    assignedTo?: true
    createdAt?: true
    updatedAt?: true
    resolvedAt?: true
  }

  export type UserIncidentMaxAggregateInputType = {
    id?: true
    userId?: true
    incidentType?: true
    description?: true
    status?: true
    priority?: true
    assignedTo?: true
    createdAt?: true
    updatedAt?: true
    resolvedAt?: true
  }

  export type UserIncidentCountAggregateInputType = {
    id?: true
    userId?: true
    incidentType?: true
    description?: true
    status?: true
    priority?: true
    assignedTo?: true
    metadata?: true
    createdAt?: true
    updatedAt?: true
    resolvedAt?: true
    _all?: true
  }

  export type UserIncidentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserIncident to aggregate.
     */
    where?: UserIncidentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserIncidents to fetch.
     */
    orderBy?: UserIncidentOrderByWithRelationInput | UserIncidentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserIncidentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserIncidents from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserIncidents.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned UserIncidents
    **/
    _count?: true | UserIncidentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserIncidentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserIncidentMaxAggregateInputType
  }

  export type GetUserIncidentAggregateType<T extends UserIncidentAggregateArgs> = {
        [P in keyof T & keyof AggregateUserIncident]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUserIncident[P]>
      : GetScalarType<T[P], AggregateUserIncident[P]>
  }




  export type UserIncidentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserIncidentWhereInput
    orderBy?: UserIncidentOrderByWithAggregationInput | UserIncidentOrderByWithAggregationInput[]
    by: UserIncidentScalarFieldEnum[] | UserIncidentScalarFieldEnum
    having?: UserIncidentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserIncidentCountAggregateInputType | true
    _min?: UserIncidentMinAggregateInputType
    _max?: UserIncidentMaxAggregateInputType
  }

  export type UserIncidentGroupByOutputType = {
    id: string
    userId: string
    incidentType: string
    description: string
    status: string
    priority: string
    assignedTo: string | null
    metadata: JsonValue | null
    createdAt: Date
    updatedAt: Date
    resolvedAt: Date | null
    _count: UserIncidentCountAggregateOutputType | null
    _min: UserIncidentMinAggregateOutputType | null
    _max: UserIncidentMaxAggregateOutputType | null
  }

  type GetUserIncidentGroupByPayload<T extends UserIncidentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserIncidentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserIncidentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserIncidentGroupByOutputType[P]>
            : GetScalarType<T[P], UserIncidentGroupByOutputType[P]>
        }
      >
    >


  export type UserIncidentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    incidentType?: boolean
    description?: boolean
    status?: boolean
    priority?: boolean
    assignedTo?: boolean
    metadata?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    resolvedAt?: boolean
  }, ExtArgs["result"]["userIncident"]>

  export type UserIncidentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    incidentType?: boolean
    description?: boolean
    status?: boolean
    priority?: boolean
    assignedTo?: boolean
    metadata?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    resolvedAt?: boolean
  }, ExtArgs["result"]["userIncident"]>

  export type UserIncidentSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    incidentType?: boolean
    description?: boolean
    status?: boolean
    priority?: boolean
    assignedTo?: boolean
    metadata?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    resolvedAt?: boolean
  }, ExtArgs["result"]["userIncident"]>

  export type UserIncidentSelectScalar = {
    id?: boolean
    userId?: boolean
    incidentType?: boolean
    description?: boolean
    status?: boolean
    priority?: boolean
    assignedTo?: boolean
    metadata?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    resolvedAt?: boolean
  }

  export type UserIncidentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "incidentType" | "description" | "status" | "priority" | "assignedTo" | "metadata" | "createdAt" | "updatedAt" | "resolvedAt", ExtArgs["result"]["userIncident"]>

  export type $UserIncidentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "UserIncident"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      incidentType: string
      description: string
      status: string
      priority: string
      assignedTo: string | null
      metadata: Prisma.JsonValue | null
      createdAt: Date
      updatedAt: Date
      resolvedAt: Date | null
    }, ExtArgs["result"]["userIncident"]>
    composites: {}
  }

  type UserIncidentGetPayload<S extends boolean | null | undefined | UserIncidentDefaultArgs> = $Result.GetResult<Prisma.$UserIncidentPayload, S>

  type UserIncidentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserIncidentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserIncidentCountAggregateInputType | true
    }

  export interface UserIncidentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['UserIncident'], meta: { name: 'UserIncident' } }
    /**
     * Find zero or one UserIncident that matches the filter.
     * @param {UserIncidentFindUniqueArgs} args - Arguments to find a UserIncident
     * @example
     * // Get one UserIncident
     * const userIncident = await prisma.userIncident.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserIncidentFindUniqueArgs>(args: SelectSubset<T, UserIncidentFindUniqueArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one UserIncident that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserIncidentFindUniqueOrThrowArgs} args - Arguments to find a UserIncident
     * @example
     * // Get one UserIncident
     * const userIncident = await prisma.userIncident.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserIncidentFindUniqueOrThrowArgs>(args: SelectSubset<T, UserIncidentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserIncident that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentFindFirstArgs} args - Arguments to find a UserIncident
     * @example
     * // Get one UserIncident
     * const userIncident = await prisma.userIncident.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserIncidentFindFirstArgs>(args?: SelectSubset<T, UserIncidentFindFirstArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first UserIncident that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentFindFirstOrThrowArgs} args - Arguments to find a UserIncident
     * @example
     * // Get one UserIncident
     * const userIncident = await prisma.userIncident.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserIncidentFindFirstOrThrowArgs>(args?: SelectSubset<T, UserIncidentFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more UserIncidents that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all UserIncidents
     * const userIncidents = await prisma.userIncident.findMany()
     * 
     * // Get first 10 UserIncidents
     * const userIncidents = await prisma.userIncident.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userIncidentWithIdOnly = await prisma.userIncident.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserIncidentFindManyArgs>(args?: SelectSubset<T, UserIncidentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a UserIncident.
     * @param {UserIncidentCreateArgs} args - Arguments to create a UserIncident.
     * @example
     * // Create one UserIncident
     * const UserIncident = await prisma.userIncident.create({
     *   data: {
     *     // ... data to create a UserIncident
     *   }
     * })
     * 
     */
    create<T extends UserIncidentCreateArgs>(args: SelectSubset<T, UserIncidentCreateArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many UserIncidents.
     * @param {UserIncidentCreateManyArgs} args - Arguments to create many UserIncidents.
     * @example
     * // Create many UserIncidents
     * const userIncident = await prisma.userIncident.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserIncidentCreateManyArgs>(args?: SelectSubset<T, UserIncidentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many UserIncidents and returns the data saved in the database.
     * @param {UserIncidentCreateManyAndReturnArgs} args - Arguments to create many UserIncidents.
     * @example
     * // Create many UserIncidents
     * const userIncident = await prisma.userIncident.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many UserIncidents and only return the `id`
     * const userIncidentWithIdOnly = await prisma.userIncident.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserIncidentCreateManyAndReturnArgs>(args?: SelectSubset<T, UserIncidentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a UserIncident.
     * @param {UserIncidentDeleteArgs} args - Arguments to delete one UserIncident.
     * @example
     * // Delete one UserIncident
     * const UserIncident = await prisma.userIncident.delete({
     *   where: {
     *     // ... filter to delete one UserIncident
     *   }
     * })
     * 
     */
    delete<T extends UserIncidentDeleteArgs>(args: SelectSubset<T, UserIncidentDeleteArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one UserIncident.
     * @param {UserIncidentUpdateArgs} args - Arguments to update one UserIncident.
     * @example
     * // Update one UserIncident
     * const userIncident = await prisma.userIncident.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserIncidentUpdateArgs>(args: SelectSubset<T, UserIncidentUpdateArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more UserIncidents.
     * @param {UserIncidentDeleteManyArgs} args - Arguments to filter UserIncidents to delete.
     * @example
     * // Delete a few UserIncidents
     * const { count } = await prisma.userIncident.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserIncidentDeleteManyArgs>(args?: SelectSubset<T, UserIncidentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserIncidents.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many UserIncidents
     * const userIncident = await prisma.userIncident.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserIncidentUpdateManyArgs>(args: SelectSubset<T, UserIncidentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more UserIncidents and returns the data updated in the database.
     * @param {UserIncidentUpdateManyAndReturnArgs} args - Arguments to update many UserIncidents.
     * @example
     * // Update many UserIncidents
     * const userIncident = await prisma.userIncident.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more UserIncidents and only return the `id`
     * const userIncidentWithIdOnly = await prisma.userIncident.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserIncidentUpdateManyAndReturnArgs>(args: SelectSubset<T, UserIncidentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one UserIncident.
     * @param {UserIncidentUpsertArgs} args - Arguments to update or create a UserIncident.
     * @example
     * // Update or create a UserIncident
     * const userIncident = await prisma.userIncident.upsert({
     *   create: {
     *     // ... data to create a UserIncident
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the UserIncident we want to update
     *   }
     * })
     */
    upsert<T extends UserIncidentUpsertArgs>(args: SelectSubset<T, UserIncidentUpsertArgs<ExtArgs>>): Prisma__UserIncidentClient<$Result.GetResult<Prisma.$UserIncidentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of UserIncidents.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentCountArgs} args - Arguments to filter UserIncidents to count.
     * @example
     * // Count the number of UserIncidents
     * const count = await prisma.userIncident.count({
     *   where: {
     *     // ... the filter for the UserIncidents we want to count
     *   }
     * })
    **/
    count<T extends UserIncidentCountArgs>(
      args?: Subset<T, UserIncidentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserIncidentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a UserIncident.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserIncidentAggregateArgs>(args: Subset<T, UserIncidentAggregateArgs>): Prisma.PrismaPromise<GetUserIncidentAggregateType<T>>

    /**
     * Group by UserIncident.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserIncidentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserIncidentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserIncidentGroupByArgs['orderBy'] }
        : { orderBy?: UserIncidentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserIncidentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserIncidentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the UserIncident model
   */
  readonly fields: UserIncidentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for UserIncident.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserIncidentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the UserIncident model
   */
  interface UserIncidentFieldRefs {
    readonly id: FieldRef<"UserIncident", 'String'>
    readonly userId: FieldRef<"UserIncident", 'String'>
    readonly incidentType: FieldRef<"UserIncident", 'String'>
    readonly description: FieldRef<"UserIncident", 'String'>
    readonly status: FieldRef<"UserIncident", 'String'>
    readonly priority: FieldRef<"UserIncident", 'String'>
    readonly assignedTo: FieldRef<"UserIncident", 'String'>
    readonly metadata: FieldRef<"UserIncident", 'Json'>
    readonly createdAt: FieldRef<"UserIncident", 'DateTime'>
    readonly updatedAt: FieldRef<"UserIncident", 'DateTime'>
    readonly resolvedAt: FieldRef<"UserIncident", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * UserIncident findUnique
   */
  export type UserIncidentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter, which UserIncident to fetch.
     */
    where: UserIncidentWhereUniqueInput
  }

  /**
   * UserIncident findUniqueOrThrow
   */
  export type UserIncidentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter, which UserIncident to fetch.
     */
    where: UserIncidentWhereUniqueInput
  }

  /**
   * UserIncident findFirst
   */
  export type UserIncidentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter, which UserIncident to fetch.
     */
    where?: UserIncidentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserIncidents to fetch.
     */
    orderBy?: UserIncidentOrderByWithRelationInput | UserIncidentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserIncidents.
     */
    cursor?: UserIncidentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserIncidents from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserIncidents.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserIncidents.
     */
    distinct?: UserIncidentScalarFieldEnum | UserIncidentScalarFieldEnum[]
  }

  /**
   * UserIncident findFirstOrThrow
   */
  export type UserIncidentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter, which UserIncident to fetch.
     */
    where?: UserIncidentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserIncidents to fetch.
     */
    orderBy?: UserIncidentOrderByWithRelationInput | UserIncidentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for UserIncidents.
     */
    cursor?: UserIncidentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserIncidents from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserIncidents.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of UserIncidents.
     */
    distinct?: UserIncidentScalarFieldEnum | UserIncidentScalarFieldEnum[]
  }

  /**
   * UserIncident findMany
   */
  export type UserIncidentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter, which UserIncidents to fetch.
     */
    where?: UserIncidentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of UserIncidents to fetch.
     */
    orderBy?: UserIncidentOrderByWithRelationInput | UserIncidentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing UserIncidents.
     */
    cursor?: UserIncidentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` UserIncidents from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` UserIncidents.
     */
    skip?: number
    distinct?: UserIncidentScalarFieldEnum | UserIncidentScalarFieldEnum[]
  }

  /**
   * UserIncident create
   */
  export type UserIncidentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * The data needed to create a UserIncident.
     */
    data: XOR<UserIncidentCreateInput, UserIncidentUncheckedCreateInput>
  }

  /**
   * UserIncident createMany
   */
  export type UserIncidentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many UserIncidents.
     */
    data: UserIncidentCreateManyInput | UserIncidentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * UserIncident createManyAndReturn
   */
  export type UserIncidentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * The data used to create many UserIncidents.
     */
    data: UserIncidentCreateManyInput | UserIncidentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * UserIncident update
   */
  export type UserIncidentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * The data needed to update a UserIncident.
     */
    data: XOR<UserIncidentUpdateInput, UserIncidentUncheckedUpdateInput>
    /**
     * Choose, which UserIncident to update.
     */
    where: UserIncidentWhereUniqueInput
  }

  /**
   * UserIncident updateMany
   */
  export type UserIncidentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update UserIncidents.
     */
    data: XOR<UserIncidentUpdateManyMutationInput, UserIncidentUncheckedUpdateManyInput>
    /**
     * Filter which UserIncidents to update
     */
    where?: UserIncidentWhereInput
    /**
     * Limit how many UserIncidents to update.
     */
    limit?: number
  }

  /**
   * UserIncident updateManyAndReturn
   */
  export type UserIncidentUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * The data used to update UserIncidents.
     */
    data: XOR<UserIncidentUpdateManyMutationInput, UserIncidentUncheckedUpdateManyInput>
    /**
     * Filter which UserIncidents to update
     */
    where?: UserIncidentWhereInput
    /**
     * Limit how many UserIncidents to update.
     */
    limit?: number
  }

  /**
   * UserIncident upsert
   */
  export type UserIncidentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * The filter to search for the UserIncident to update in case it exists.
     */
    where: UserIncidentWhereUniqueInput
    /**
     * In case the UserIncident found by the `where` argument doesn't exist, create a new UserIncident with this data.
     */
    create: XOR<UserIncidentCreateInput, UserIncidentUncheckedCreateInput>
    /**
     * In case the UserIncident was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserIncidentUpdateInput, UserIncidentUncheckedUpdateInput>
  }

  /**
   * UserIncident delete
   */
  export type UserIncidentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
    /**
     * Filter which UserIncident to delete.
     */
    where: UserIncidentWhereUniqueInput
  }

  /**
   * UserIncident deleteMany
   */
  export type UserIncidentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which UserIncidents to delete
     */
    where?: UserIncidentWhereInput
    /**
     * Limit how many UserIncidents to delete.
     */
    limit?: number
  }

  /**
   * UserIncident without action
   */
  export type UserIncidentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserIncident
     */
    select?: UserIncidentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the UserIncident
     */
    omit?: UserIncidentOmit<ExtArgs> | null
  }


  /**
   * Model CrosscoreRequest
   */

  export type AggregateCrosscoreRequest = {
    _count: CrosscoreRequestCountAggregateOutputType | null
    _min: CrosscoreRequestMinAggregateOutputType | null
    _max: CrosscoreRequestMaxAggregateOutputType | null
  }

  export type CrosscoreRequestMinAggregateOutputType = {
    id: string | null
    userId: string | null
    requestId: string | null
    verificationType: string | null
    status: string | null
    experianRequestId: string | null
    experianStatus: string | null
    errorMessage: string | null
    webhookReceived: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
    completedAt: Date | null
  }

  export type CrosscoreRequestMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    requestId: string | null
    verificationType: string | null
    status: string | null
    experianRequestId: string | null
    experianStatus: string | null
    errorMessage: string | null
    webhookReceived: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
    completedAt: Date | null
  }

  export type CrosscoreRequestCountAggregateOutputType = {
    id: number
    userId: number
    requestId: number
    verificationType: number
    status: number
    requestData: number
    responseData: number
    experianRequestId: number
    experianStatus: number
    errorMessage: number
    webhookReceived: number
    webhookData: number
    createdAt: number
    updatedAt: number
    completedAt: number
    _all: number
  }


  export type CrosscoreRequestMinAggregateInputType = {
    id?: true
    userId?: true
    requestId?: true
    verificationType?: true
    status?: true
    experianRequestId?: true
    experianStatus?: true
    errorMessage?: true
    webhookReceived?: true
    createdAt?: true
    updatedAt?: true
    completedAt?: true
  }

  export type CrosscoreRequestMaxAggregateInputType = {
    id?: true
    userId?: true
    requestId?: true
    verificationType?: true
    status?: true
    experianRequestId?: true
    experianStatus?: true
    errorMessage?: true
    webhookReceived?: true
    createdAt?: true
    updatedAt?: true
    completedAt?: true
  }

  export type CrosscoreRequestCountAggregateInputType = {
    id?: true
    userId?: true
    requestId?: true
    verificationType?: true
    status?: true
    requestData?: true
    responseData?: true
    experianRequestId?: true
    experianStatus?: true
    errorMessage?: true
    webhookReceived?: true
    webhookData?: true
    createdAt?: true
    updatedAt?: true
    completedAt?: true
    _all?: true
  }

  export type CrosscoreRequestAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrosscoreRequest to aggregate.
     */
    where?: CrosscoreRequestWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrosscoreRequests to fetch.
     */
    orderBy?: CrosscoreRequestOrderByWithRelationInput | CrosscoreRequestOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CrosscoreRequestWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrosscoreRequests from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrosscoreRequests.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned CrosscoreRequests
    **/
    _count?: true | CrosscoreRequestCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CrosscoreRequestMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CrosscoreRequestMaxAggregateInputType
  }

  export type GetCrosscoreRequestAggregateType<T extends CrosscoreRequestAggregateArgs> = {
        [P in keyof T & keyof AggregateCrosscoreRequest]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCrosscoreRequest[P]>
      : GetScalarType<T[P], AggregateCrosscoreRequest[P]>
  }




  export type CrosscoreRequestGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CrosscoreRequestWhereInput
    orderBy?: CrosscoreRequestOrderByWithAggregationInput | CrosscoreRequestOrderByWithAggregationInput[]
    by: CrosscoreRequestScalarFieldEnum[] | CrosscoreRequestScalarFieldEnum
    having?: CrosscoreRequestScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CrosscoreRequestCountAggregateInputType | true
    _min?: CrosscoreRequestMinAggregateInputType
    _max?: CrosscoreRequestMaxAggregateInputType
  }

  export type CrosscoreRequestGroupByOutputType = {
    id: string
    userId: string
    requestId: string
    verificationType: string
    status: string
    requestData: JsonValue
    responseData: JsonValue | null
    experianRequestId: string | null
    experianStatus: string | null
    errorMessage: string | null
    webhookReceived: boolean
    webhookData: JsonValue | null
    createdAt: Date
    updatedAt: Date
    completedAt: Date | null
    _count: CrosscoreRequestCountAggregateOutputType | null
    _min: CrosscoreRequestMinAggregateOutputType | null
    _max: CrosscoreRequestMaxAggregateOutputType | null
  }

  type GetCrosscoreRequestGroupByPayload<T extends CrosscoreRequestGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CrosscoreRequestGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CrosscoreRequestGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CrosscoreRequestGroupByOutputType[P]>
            : GetScalarType<T[P], CrosscoreRequestGroupByOutputType[P]>
        }
      >
    >


  export type CrosscoreRequestSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    requestId?: boolean
    verificationType?: boolean
    status?: boolean
    requestData?: boolean
    responseData?: boolean
    experianRequestId?: boolean
    experianStatus?: boolean
    errorMessage?: boolean
    webhookReceived?: boolean
    webhookData?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    completedAt?: boolean
  }, ExtArgs["result"]["crosscoreRequest"]>

  export type CrosscoreRequestSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    requestId?: boolean
    verificationType?: boolean
    status?: boolean
    requestData?: boolean
    responseData?: boolean
    experianRequestId?: boolean
    experianStatus?: boolean
    errorMessage?: boolean
    webhookReceived?: boolean
    webhookData?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    completedAt?: boolean
  }, ExtArgs["result"]["crosscoreRequest"]>

  export type CrosscoreRequestSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    requestId?: boolean
    verificationType?: boolean
    status?: boolean
    requestData?: boolean
    responseData?: boolean
    experianRequestId?: boolean
    experianStatus?: boolean
    errorMessage?: boolean
    webhookReceived?: boolean
    webhookData?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    completedAt?: boolean
  }, ExtArgs["result"]["crosscoreRequest"]>

  export type CrosscoreRequestSelectScalar = {
    id?: boolean
    userId?: boolean
    requestId?: boolean
    verificationType?: boolean
    status?: boolean
    requestData?: boolean
    responseData?: boolean
    experianRequestId?: boolean
    experianStatus?: boolean
    errorMessage?: boolean
    webhookReceived?: boolean
    webhookData?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    completedAt?: boolean
  }

  export type CrosscoreRequestOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "requestId" | "verificationType" | "status" | "requestData" | "responseData" | "experianRequestId" | "experianStatus" | "errorMessage" | "webhookReceived" | "webhookData" | "createdAt" | "updatedAt" | "completedAt", ExtArgs["result"]["crosscoreRequest"]>

  export type $CrosscoreRequestPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "CrosscoreRequest"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      requestId: string
      verificationType: string
      status: string
      requestData: Prisma.JsonValue
      responseData: Prisma.JsonValue | null
      experianRequestId: string | null
      experianStatus: string | null
      errorMessage: string | null
      webhookReceived: boolean
      webhookData: Prisma.JsonValue | null
      createdAt: Date
      updatedAt: Date
      completedAt: Date | null
    }, ExtArgs["result"]["crosscoreRequest"]>
    composites: {}
  }

  type CrosscoreRequestGetPayload<S extends boolean | null | undefined | CrosscoreRequestDefaultArgs> = $Result.GetResult<Prisma.$CrosscoreRequestPayload, S>

  type CrosscoreRequestCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<CrosscoreRequestFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: CrosscoreRequestCountAggregateInputType | true
    }

  export interface CrosscoreRequestDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['CrosscoreRequest'], meta: { name: 'CrosscoreRequest' } }
    /**
     * Find zero or one CrosscoreRequest that matches the filter.
     * @param {CrosscoreRequestFindUniqueArgs} args - Arguments to find a CrosscoreRequest
     * @example
     * // Get one CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CrosscoreRequestFindUniqueArgs>(args: SelectSubset<T, CrosscoreRequestFindUniqueArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one CrosscoreRequest that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {CrosscoreRequestFindUniqueOrThrowArgs} args - Arguments to find a CrosscoreRequest
     * @example
     * // Get one CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CrosscoreRequestFindUniqueOrThrowArgs>(args: SelectSubset<T, CrosscoreRequestFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first CrosscoreRequest that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestFindFirstArgs} args - Arguments to find a CrosscoreRequest
     * @example
     * // Get one CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CrosscoreRequestFindFirstArgs>(args?: SelectSubset<T, CrosscoreRequestFindFirstArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first CrosscoreRequest that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestFindFirstOrThrowArgs} args - Arguments to find a CrosscoreRequest
     * @example
     * // Get one CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CrosscoreRequestFindFirstOrThrowArgs>(args?: SelectSubset<T, CrosscoreRequestFindFirstOrThrowArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more CrosscoreRequests that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all CrosscoreRequests
     * const crosscoreRequests = await prisma.crosscoreRequest.findMany()
     * 
     * // Get first 10 CrosscoreRequests
     * const crosscoreRequests = await prisma.crosscoreRequest.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const crosscoreRequestWithIdOnly = await prisma.crosscoreRequest.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CrosscoreRequestFindManyArgs>(args?: SelectSubset<T, CrosscoreRequestFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a CrosscoreRequest.
     * @param {CrosscoreRequestCreateArgs} args - Arguments to create a CrosscoreRequest.
     * @example
     * // Create one CrosscoreRequest
     * const CrosscoreRequest = await prisma.crosscoreRequest.create({
     *   data: {
     *     // ... data to create a CrosscoreRequest
     *   }
     * })
     * 
     */
    create<T extends CrosscoreRequestCreateArgs>(args: SelectSubset<T, CrosscoreRequestCreateArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many CrosscoreRequests.
     * @param {CrosscoreRequestCreateManyArgs} args - Arguments to create many CrosscoreRequests.
     * @example
     * // Create many CrosscoreRequests
     * const crosscoreRequest = await prisma.crosscoreRequest.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CrosscoreRequestCreateManyArgs>(args?: SelectSubset<T, CrosscoreRequestCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many CrosscoreRequests and returns the data saved in the database.
     * @param {CrosscoreRequestCreateManyAndReturnArgs} args - Arguments to create many CrosscoreRequests.
     * @example
     * // Create many CrosscoreRequests
     * const crosscoreRequest = await prisma.crosscoreRequest.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many CrosscoreRequests and only return the `id`
     * const crosscoreRequestWithIdOnly = await prisma.crosscoreRequest.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CrosscoreRequestCreateManyAndReturnArgs>(args?: SelectSubset<T, CrosscoreRequestCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a CrosscoreRequest.
     * @param {CrosscoreRequestDeleteArgs} args - Arguments to delete one CrosscoreRequest.
     * @example
     * // Delete one CrosscoreRequest
     * const CrosscoreRequest = await prisma.crosscoreRequest.delete({
     *   where: {
     *     // ... filter to delete one CrosscoreRequest
     *   }
     * })
     * 
     */
    delete<T extends CrosscoreRequestDeleteArgs>(args: SelectSubset<T, CrosscoreRequestDeleteArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one CrosscoreRequest.
     * @param {CrosscoreRequestUpdateArgs} args - Arguments to update one CrosscoreRequest.
     * @example
     * // Update one CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CrosscoreRequestUpdateArgs>(args: SelectSubset<T, CrosscoreRequestUpdateArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more CrosscoreRequests.
     * @param {CrosscoreRequestDeleteManyArgs} args - Arguments to filter CrosscoreRequests to delete.
     * @example
     * // Delete a few CrosscoreRequests
     * const { count } = await prisma.crosscoreRequest.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CrosscoreRequestDeleteManyArgs>(args?: SelectSubset<T, CrosscoreRequestDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CrosscoreRequests.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many CrosscoreRequests
     * const crosscoreRequest = await prisma.crosscoreRequest.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CrosscoreRequestUpdateManyArgs>(args: SelectSubset<T, CrosscoreRequestUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CrosscoreRequests and returns the data updated in the database.
     * @param {CrosscoreRequestUpdateManyAndReturnArgs} args - Arguments to update many CrosscoreRequests.
     * @example
     * // Update many CrosscoreRequests
     * const crosscoreRequest = await prisma.crosscoreRequest.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more CrosscoreRequests and only return the `id`
     * const crosscoreRequestWithIdOnly = await prisma.crosscoreRequest.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends CrosscoreRequestUpdateManyAndReturnArgs>(args: SelectSubset<T, CrosscoreRequestUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one CrosscoreRequest.
     * @param {CrosscoreRequestUpsertArgs} args - Arguments to update or create a CrosscoreRequest.
     * @example
     * // Update or create a CrosscoreRequest
     * const crosscoreRequest = await prisma.crosscoreRequest.upsert({
     *   create: {
     *     // ... data to create a CrosscoreRequest
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the CrosscoreRequest we want to update
     *   }
     * })
     */
    upsert<T extends CrosscoreRequestUpsertArgs>(args: SelectSubset<T, CrosscoreRequestUpsertArgs<ExtArgs>>): Prisma__CrosscoreRequestClient<$Result.GetResult<Prisma.$CrosscoreRequestPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of CrosscoreRequests.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestCountArgs} args - Arguments to filter CrosscoreRequests to count.
     * @example
     * // Count the number of CrosscoreRequests
     * const count = await prisma.crosscoreRequest.count({
     *   where: {
     *     // ... the filter for the CrosscoreRequests we want to count
     *   }
     * })
    **/
    count<T extends CrosscoreRequestCountArgs>(
      args?: Subset<T, CrosscoreRequestCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CrosscoreRequestCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a CrosscoreRequest.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CrosscoreRequestAggregateArgs>(args: Subset<T, CrosscoreRequestAggregateArgs>): Prisma.PrismaPromise<GetCrosscoreRequestAggregateType<T>>

    /**
     * Group by CrosscoreRequest.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrosscoreRequestGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CrosscoreRequestGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CrosscoreRequestGroupByArgs['orderBy'] }
        : { orderBy?: CrosscoreRequestGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CrosscoreRequestGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCrosscoreRequestGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the CrosscoreRequest model
   */
  readonly fields: CrosscoreRequestFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for CrosscoreRequest.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CrosscoreRequestClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the CrosscoreRequest model
   */
  interface CrosscoreRequestFieldRefs {
    readonly id: FieldRef<"CrosscoreRequest", 'String'>
    readonly userId: FieldRef<"CrosscoreRequest", 'String'>
    readonly requestId: FieldRef<"CrosscoreRequest", 'String'>
    readonly verificationType: FieldRef<"CrosscoreRequest", 'String'>
    readonly status: FieldRef<"CrosscoreRequest", 'String'>
    readonly requestData: FieldRef<"CrosscoreRequest", 'Json'>
    readonly responseData: FieldRef<"CrosscoreRequest", 'Json'>
    readonly experianRequestId: FieldRef<"CrosscoreRequest", 'String'>
    readonly experianStatus: FieldRef<"CrosscoreRequest", 'String'>
    readonly errorMessage: FieldRef<"CrosscoreRequest", 'String'>
    readonly webhookReceived: FieldRef<"CrosscoreRequest", 'Boolean'>
    readonly webhookData: FieldRef<"CrosscoreRequest", 'Json'>
    readonly createdAt: FieldRef<"CrosscoreRequest", 'DateTime'>
    readonly updatedAt: FieldRef<"CrosscoreRequest", 'DateTime'>
    readonly completedAt: FieldRef<"CrosscoreRequest", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * CrosscoreRequest findUnique
   */
  export type CrosscoreRequestFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter, which CrosscoreRequest to fetch.
     */
    where: CrosscoreRequestWhereUniqueInput
  }

  /**
   * CrosscoreRequest findUniqueOrThrow
   */
  export type CrosscoreRequestFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter, which CrosscoreRequest to fetch.
     */
    where: CrosscoreRequestWhereUniqueInput
  }

  /**
   * CrosscoreRequest findFirst
   */
  export type CrosscoreRequestFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter, which CrosscoreRequest to fetch.
     */
    where?: CrosscoreRequestWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrosscoreRequests to fetch.
     */
    orderBy?: CrosscoreRequestOrderByWithRelationInput | CrosscoreRequestOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrosscoreRequests.
     */
    cursor?: CrosscoreRequestWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrosscoreRequests from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrosscoreRequests.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrosscoreRequests.
     */
    distinct?: CrosscoreRequestScalarFieldEnum | CrosscoreRequestScalarFieldEnum[]
  }

  /**
   * CrosscoreRequest findFirstOrThrow
   */
  export type CrosscoreRequestFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter, which CrosscoreRequest to fetch.
     */
    where?: CrosscoreRequestWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrosscoreRequests to fetch.
     */
    orderBy?: CrosscoreRequestOrderByWithRelationInput | CrosscoreRequestOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrosscoreRequests.
     */
    cursor?: CrosscoreRequestWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrosscoreRequests from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrosscoreRequests.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrosscoreRequests.
     */
    distinct?: CrosscoreRequestScalarFieldEnum | CrosscoreRequestScalarFieldEnum[]
  }

  /**
   * CrosscoreRequest findMany
   */
  export type CrosscoreRequestFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter, which CrosscoreRequests to fetch.
     */
    where?: CrosscoreRequestWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrosscoreRequests to fetch.
     */
    orderBy?: CrosscoreRequestOrderByWithRelationInput | CrosscoreRequestOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing CrosscoreRequests.
     */
    cursor?: CrosscoreRequestWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrosscoreRequests from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrosscoreRequests.
     */
    skip?: number
    distinct?: CrosscoreRequestScalarFieldEnum | CrosscoreRequestScalarFieldEnum[]
  }

  /**
   * CrosscoreRequest create
   */
  export type CrosscoreRequestCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * The data needed to create a CrosscoreRequest.
     */
    data: XOR<CrosscoreRequestCreateInput, CrosscoreRequestUncheckedCreateInput>
  }

  /**
   * CrosscoreRequest createMany
   */
  export type CrosscoreRequestCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many CrosscoreRequests.
     */
    data: CrosscoreRequestCreateManyInput | CrosscoreRequestCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * CrosscoreRequest createManyAndReturn
   */
  export type CrosscoreRequestCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * The data used to create many CrosscoreRequests.
     */
    data: CrosscoreRequestCreateManyInput | CrosscoreRequestCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * CrosscoreRequest update
   */
  export type CrosscoreRequestUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * The data needed to update a CrosscoreRequest.
     */
    data: XOR<CrosscoreRequestUpdateInput, CrosscoreRequestUncheckedUpdateInput>
    /**
     * Choose, which CrosscoreRequest to update.
     */
    where: CrosscoreRequestWhereUniqueInput
  }

  /**
   * CrosscoreRequest updateMany
   */
  export type CrosscoreRequestUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update CrosscoreRequests.
     */
    data: XOR<CrosscoreRequestUpdateManyMutationInput, CrosscoreRequestUncheckedUpdateManyInput>
    /**
     * Filter which CrosscoreRequests to update
     */
    where?: CrosscoreRequestWhereInput
    /**
     * Limit how many CrosscoreRequests to update.
     */
    limit?: number
  }

  /**
   * CrosscoreRequest updateManyAndReturn
   */
  export type CrosscoreRequestUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * The data used to update CrosscoreRequests.
     */
    data: XOR<CrosscoreRequestUpdateManyMutationInput, CrosscoreRequestUncheckedUpdateManyInput>
    /**
     * Filter which CrosscoreRequests to update
     */
    where?: CrosscoreRequestWhereInput
    /**
     * Limit how many CrosscoreRequests to update.
     */
    limit?: number
  }

  /**
   * CrosscoreRequest upsert
   */
  export type CrosscoreRequestUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * The filter to search for the CrosscoreRequest to update in case it exists.
     */
    where: CrosscoreRequestWhereUniqueInput
    /**
     * In case the CrosscoreRequest found by the `where` argument doesn't exist, create a new CrosscoreRequest with this data.
     */
    create: XOR<CrosscoreRequestCreateInput, CrosscoreRequestUncheckedCreateInput>
    /**
     * In case the CrosscoreRequest was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CrosscoreRequestUpdateInput, CrosscoreRequestUncheckedUpdateInput>
  }

  /**
   * CrosscoreRequest delete
   */
  export type CrosscoreRequestDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
    /**
     * Filter which CrosscoreRequest to delete.
     */
    where: CrosscoreRequestWhereUniqueInput
  }

  /**
   * CrosscoreRequest deleteMany
   */
  export type CrosscoreRequestDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrosscoreRequests to delete
     */
    where?: CrosscoreRequestWhereInput
    /**
     * Limit how many CrosscoreRequests to delete.
     */
    limit?: number
  }

  /**
   * CrosscoreRequest without action
   */
  export type CrosscoreRequestDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrosscoreRequest
     */
    select?: CrosscoreRequestSelect<ExtArgs> | null
    /**
     * Omit specific fields from the CrosscoreRequest
     */
    omit?: CrosscoreRequestOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserIncidentScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    incidentType: 'incidentType',
    description: 'description',
    status: 'status',
    priority: 'priority',
    assignedTo: 'assignedTo',
    metadata: 'metadata',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    resolvedAt: 'resolvedAt'
  };

  export type UserIncidentScalarFieldEnum = (typeof UserIncidentScalarFieldEnum)[keyof typeof UserIncidentScalarFieldEnum]


  export const CrosscoreRequestScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    requestId: 'requestId',
    verificationType: 'verificationType',
    status: 'status',
    requestData: 'requestData',
    responseData: 'responseData',
    experianRequestId: 'experianRequestId',
    experianStatus: 'experianStatus',
    errorMessage: 'errorMessage',
    webhookReceived: 'webhookReceived',
    webhookData: 'webhookData',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    completedAt: 'completedAt'
  };

  export type CrosscoreRequestScalarFieldEnum = (typeof CrosscoreRequestScalarFieldEnum)[keyof typeof CrosscoreRequestScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type UserIncidentWhereInput = {
    AND?: UserIncidentWhereInput | UserIncidentWhereInput[]
    OR?: UserIncidentWhereInput[]
    NOT?: UserIncidentWhereInput | UserIncidentWhereInput[]
    id?: StringFilter<"UserIncident"> | string
    userId?: StringFilter<"UserIncident"> | string
    incidentType?: StringFilter<"UserIncident"> | string
    description?: StringFilter<"UserIncident"> | string
    status?: StringFilter<"UserIncident"> | string
    priority?: StringFilter<"UserIncident"> | string
    assignedTo?: StringNullableFilter<"UserIncident"> | string | null
    metadata?: JsonNullableFilter<"UserIncident">
    createdAt?: DateTimeFilter<"UserIncident"> | Date | string
    updatedAt?: DateTimeFilter<"UserIncident"> | Date | string
    resolvedAt?: DateTimeNullableFilter<"UserIncident"> | Date | string | null
  }

  export type UserIncidentOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    incidentType?: SortOrder
    description?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    assignedTo?: SortOrderInput | SortOrder
    metadata?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    resolvedAt?: SortOrderInput | SortOrder
  }

  export type UserIncidentWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: UserIncidentWhereInput | UserIncidentWhereInput[]
    OR?: UserIncidentWhereInput[]
    NOT?: UserIncidentWhereInput | UserIncidentWhereInput[]
    userId?: StringFilter<"UserIncident"> | string
    incidentType?: StringFilter<"UserIncident"> | string
    description?: StringFilter<"UserIncident"> | string
    status?: StringFilter<"UserIncident"> | string
    priority?: StringFilter<"UserIncident"> | string
    assignedTo?: StringNullableFilter<"UserIncident"> | string | null
    metadata?: JsonNullableFilter<"UserIncident">
    createdAt?: DateTimeFilter<"UserIncident"> | Date | string
    updatedAt?: DateTimeFilter<"UserIncident"> | Date | string
    resolvedAt?: DateTimeNullableFilter<"UserIncident"> | Date | string | null
  }, "id">

  export type UserIncidentOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    incidentType?: SortOrder
    description?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    assignedTo?: SortOrderInput | SortOrder
    metadata?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    resolvedAt?: SortOrderInput | SortOrder
    _count?: UserIncidentCountOrderByAggregateInput
    _max?: UserIncidentMaxOrderByAggregateInput
    _min?: UserIncidentMinOrderByAggregateInput
  }

  export type UserIncidentScalarWhereWithAggregatesInput = {
    AND?: UserIncidentScalarWhereWithAggregatesInput | UserIncidentScalarWhereWithAggregatesInput[]
    OR?: UserIncidentScalarWhereWithAggregatesInput[]
    NOT?: UserIncidentScalarWhereWithAggregatesInput | UserIncidentScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"UserIncident"> | string
    userId?: StringWithAggregatesFilter<"UserIncident"> | string
    incidentType?: StringWithAggregatesFilter<"UserIncident"> | string
    description?: StringWithAggregatesFilter<"UserIncident"> | string
    status?: StringWithAggregatesFilter<"UserIncident"> | string
    priority?: StringWithAggregatesFilter<"UserIncident"> | string
    assignedTo?: StringNullableWithAggregatesFilter<"UserIncident"> | string | null
    metadata?: JsonNullableWithAggregatesFilter<"UserIncident">
    createdAt?: DateTimeWithAggregatesFilter<"UserIncident"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"UserIncident"> | Date | string
    resolvedAt?: DateTimeNullableWithAggregatesFilter<"UserIncident"> | Date | string | null
  }

  export type CrosscoreRequestWhereInput = {
    AND?: CrosscoreRequestWhereInput | CrosscoreRequestWhereInput[]
    OR?: CrosscoreRequestWhereInput[]
    NOT?: CrosscoreRequestWhereInput | CrosscoreRequestWhereInput[]
    id?: StringFilter<"CrosscoreRequest"> | string
    userId?: StringFilter<"CrosscoreRequest"> | string
    requestId?: StringFilter<"CrosscoreRequest"> | string
    verificationType?: StringFilter<"CrosscoreRequest"> | string
    status?: StringFilter<"CrosscoreRequest"> | string
    requestData?: JsonFilter<"CrosscoreRequest">
    responseData?: JsonNullableFilter<"CrosscoreRequest">
    experianRequestId?: StringNullableFilter<"CrosscoreRequest"> | string | null
    experianStatus?: StringNullableFilter<"CrosscoreRequest"> | string | null
    errorMessage?: StringNullableFilter<"CrosscoreRequest"> | string | null
    webhookReceived?: BoolFilter<"CrosscoreRequest"> | boolean
    webhookData?: JsonNullableFilter<"CrosscoreRequest">
    createdAt?: DateTimeFilter<"CrosscoreRequest"> | Date | string
    updatedAt?: DateTimeFilter<"CrosscoreRequest"> | Date | string
    completedAt?: DateTimeNullableFilter<"CrosscoreRequest"> | Date | string | null
  }

  export type CrosscoreRequestOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    requestId?: SortOrder
    verificationType?: SortOrder
    status?: SortOrder
    requestData?: SortOrder
    responseData?: SortOrderInput | SortOrder
    experianRequestId?: SortOrderInput | SortOrder
    experianStatus?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    webhookReceived?: SortOrder
    webhookData?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    completedAt?: SortOrderInput | SortOrder
  }

  export type CrosscoreRequestWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    requestId?: string
    AND?: CrosscoreRequestWhereInput | CrosscoreRequestWhereInput[]
    OR?: CrosscoreRequestWhereInput[]
    NOT?: CrosscoreRequestWhereInput | CrosscoreRequestWhereInput[]
    userId?: StringFilter<"CrosscoreRequest"> | string
    verificationType?: StringFilter<"CrosscoreRequest"> | string
    status?: StringFilter<"CrosscoreRequest"> | string
    requestData?: JsonFilter<"CrosscoreRequest">
    responseData?: JsonNullableFilter<"CrosscoreRequest">
    experianRequestId?: StringNullableFilter<"CrosscoreRequest"> | string | null
    experianStatus?: StringNullableFilter<"CrosscoreRequest"> | string | null
    errorMessage?: StringNullableFilter<"CrosscoreRequest"> | string | null
    webhookReceived?: BoolFilter<"CrosscoreRequest"> | boolean
    webhookData?: JsonNullableFilter<"CrosscoreRequest">
    createdAt?: DateTimeFilter<"CrosscoreRequest"> | Date | string
    updatedAt?: DateTimeFilter<"CrosscoreRequest"> | Date | string
    completedAt?: DateTimeNullableFilter<"CrosscoreRequest"> | Date | string | null
  }, "id" | "requestId">

  export type CrosscoreRequestOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    requestId?: SortOrder
    verificationType?: SortOrder
    status?: SortOrder
    requestData?: SortOrder
    responseData?: SortOrderInput | SortOrder
    experianRequestId?: SortOrderInput | SortOrder
    experianStatus?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    webhookReceived?: SortOrder
    webhookData?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    completedAt?: SortOrderInput | SortOrder
    _count?: CrosscoreRequestCountOrderByAggregateInput
    _max?: CrosscoreRequestMaxOrderByAggregateInput
    _min?: CrosscoreRequestMinOrderByAggregateInput
  }

  export type CrosscoreRequestScalarWhereWithAggregatesInput = {
    AND?: CrosscoreRequestScalarWhereWithAggregatesInput | CrosscoreRequestScalarWhereWithAggregatesInput[]
    OR?: CrosscoreRequestScalarWhereWithAggregatesInput[]
    NOT?: CrosscoreRequestScalarWhereWithAggregatesInput | CrosscoreRequestScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"CrosscoreRequest"> | string
    userId?: StringWithAggregatesFilter<"CrosscoreRequest"> | string
    requestId?: StringWithAggregatesFilter<"CrosscoreRequest"> | string
    verificationType?: StringWithAggregatesFilter<"CrosscoreRequest"> | string
    status?: StringWithAggregatesFilter<"CrosscoreRequest"> | string
    requestData?: JsonWithAggregatesFilter<"CrosscoreRequest">
    responseData?: JsonNullableWithAggregatesFilter<"CrosscoreRequest">
    experianRequestId?: StringNullableWithAggregatesFilter<"CrosscoreRequest"> | string | null
    experianStatus?: StringNullableWithAggregatesFilter<"CrosscoreRequest"> | string | null
    errorMessage?: StringNullableWithAggregatesFilter<"CrosscoreRequest"> | string | null
    webhookReceived?: BoolWithAggregatesFilter<"CrosscoreRequest"> | boolean
    webhookData?: JsonNullableWithAggregatesFilter<"CrosscoreRequest">
    createdAt?: DateTimeWithAggregatesFilter<"CrosscoreRequest"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"CrosscoreRequest"> | Date | string
    completedAt?: DateTimeNullableWithAggregatesFilter<"CrosscoreRequest"> | Date | string | null
  }

  export type UserIncidentCreateInput = {
    id?: string
    userId: string
    incidentType: string
    description: string
    status?: string
    priority?: string
    assignedTo?: string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    resolvedAt?: Date | string | null
  }

  export type UserIncidentUncheckedCreateInput = {
    id?: string
    userId: string
    incidentType: string
    description: string
    status?: string
    priority?: string
    assignedTo?: string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    resolvedAt?: Date | string | null
  }

  export type UserIncidentUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    incidentType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    assignedTo?: NullableStringFieldUpdateOperationsInput | string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resolvedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type UserIncidentUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    incidentType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    assignedTo?: NullableStringFieldUpdateOperationsInput | string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resolvedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type UserIncidentCreateManyInput = {
    id?: string
    userId: string
    incidentType: string
    description: string
    status?: string
    priority?: string
    assignedTo?: string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    resolvedAt?: Date | string | null
  }

  export type UserIncidentUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    incidentType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    assignedTo?: NullableStringFieldUpdateOperationsInput | string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resolvedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type UserIncidentUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    incidentType?: StringFieldUpdateOperationsInput | string
    description?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    priority?: StringFieldUpdateOperationsInput | string
    assignedTo?: NullableStringFieldUpdateOperationsInput | string | null
    metadata?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    resolvedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrosscoreRequestCreateInput = {
    id?: string
    userId: string
    requestId: string
    verificationType: string
    status?: string
    requestData: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: string | null
    experianStatus?: string | null
    errorMessage?: string | null
    webhookReceived?: boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    completedAt?: Date | string | null
  }

  export type CrosscoreRequestUncheckedCreateInput = {
    id?: string
    userId: string
    requestId: string
    verificationType: string
    status?: string
    requestData: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: string | null
    experianStatus?: string | null
    errorMessage?: string | null
    webhookReceived?: boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    completedAt?: Date | string | null
  }

  export type CrosscoreRequestUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    requestId?: StringFieldUpdateOperationsInput | string
    verificationType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    requestData?: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: NullableStringFieldUpdateOperationsInput | string | null
    experianStatus?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    webhookReceived?: BoolFieldUpdateOperationsInput | boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrosscoreRequestUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    requestId?: StringFieldUpdateOperationsInput | string
    verificationType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    requestData?: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: NullableStringFieldUpdateOperationsInput | string | null
    experianStatus?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    webhookReceived?: BoolFieldUpdateOperationsInput | boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrosscoreRequestCreateManyInput = {
    id?: string
    userId: string
    requestId: string
    verificationType: string
    status?: string
    requestData: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: string | null
    experianStatus?: string | null
    errorMessage?: string | null
    webhookReceived?: boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    updatedAt?: Date | string
    completedAt?: Date | string | null
  }

  export type CrosscoreRequestUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    requestId?: StringFieldUpdateOperationsInput | string
    verificationType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    requestData?: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: NullableStringFieldUpdateOperationsInput | string | null
    experianStatus?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    webhookReceived?: BoolFieldUpdateOperationsInput | boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrosscoreRequestUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    requestId?: StringFieldUpdateOperationsInput | string
    verificationType?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    requestData?: JsonNullValueInput | InputJsonValue
    responseData?: NullableJsonNullValueInput | InputJsonValue
    experianRequestId?: NullableStringFieldUpdateOperationsInput | string | null
    experianStatus?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableStringFieldUpdateOperationsInput | string | null
    webhookReceived?: BoolFieldUpdateOperationsInput | boolean
    webhookData?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    completedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type UserIncidentCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    incidentType?: SortOrder
    description?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    assignedTo?: SortOrder
    metadata?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    resolvedAt?: SortOrder
  }

  export type UserIncidentMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    incidentType?: SortOrder
    description?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    assignedTo?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    resolvedAt?: SortOrder
  }

  export type UserIncidentMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    incidentType?: SortOrder
    description?: SortOrder
    status?: SortOrder
    priority?: SortOrder
    assignedTo?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    resolvedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type CrosscoreRequestCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    requestId?: SortOrder
    verificationType?: SortOrder
    status?: SortOrder
    requestData?: SortOrder
    responseData?: SortOrder
    experianRequestId?: SortOrder
    experianStatus?: SortOrder
    errorMessage?: SortOrder
    webhookReceived?: SortOrder
    webhookData?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    completedAt?: SortOrder
  }

  export type CrosscoreRequestMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    requestId?: SortOrder
    verificationType?: SortOrder
    status?: SortOrder
    experianRequestId?: SortOrder
    experianStatus?: SortOrder
    errorMessage?: SortOrder
    webhookReceived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    completedAt?: SortOrder
  }

  export type CrosscoreRequestMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    requestId?: SortOrder
    verificationType?: SortOrder
    status?: SortOrder
    experianRequestId?: SortOrder
    experianStatus?: SortOrder
    errorMessage?: SortOrder
    webhookReceived?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    completedAt?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}