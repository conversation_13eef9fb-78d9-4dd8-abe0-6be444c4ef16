// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserIncident {
  id           String    @id @default(cuid())
  userId       String
  incidentType String
  description  String
  status       String    @default("pending")
  priority     String    @default("medium")
  assignedTo   String?
  metadata     Json?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  resolvedAt   DateTime?

  @@index([userId, status])
  @@index([createdAt])
  @@index([status, priority])
  @@map("user_incidents")
}

model CrosscoreRequest {
  id                String    @id @default(cuid())
  userId            String
  requestId         String    @unique
  verificationType  String
  status            String    @default("pending")
  requestData       Json
  responseData      Json?
  experianRequestId String?
  experianStatus    String?
  errorMessage      String?
  webhookReceived   Boolean   @default(false)
  webhookData       Json?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  completedAt       DateTime?

  @@index([userId, status])
  @@index([createdAt])
  @@index([status, verificationType])
  @@index([experianRequestId])
  @@map("crosscore_requests")
}
